# Client Analytics Parser


## Project description

This project will consume directly a kafka queue from the websocket server project (https://kvasir.hexaglobe.net/advanced-suite/back/client-analytics-client-server) and produce to 5 differents topics (global, stream, heartbeat, quality and event)

## Getting started

To start the project you will need to have the databases/ directory create containing geoip databases, dboperator.mmdb (strategy routing) db and udger in the project's root.
In the conf file, set correctly all the paths and the latest available version of the dboperator database containing datetime on it.
Futhermore, you will need to have the hexaparser library available in the project's root.
in order to do so, you can do the following commands:
    
```bash
git clone https://kvasir.hexaglobe.net/hexaglobe/hexa-parser-udger.git hexaparser
```

You should also modify the config file of the parser to adapt it to your needs.

## Then build and run

sudo docker-compose build
sudo docker-compose up -d

(As we use memory limit in docker-compose, if your docker-compose version is too old you will have to use the --compatibility option)

(Due to python memory usage, the docker should be restart frequently (depending on your resources) to avoid using too much memory)