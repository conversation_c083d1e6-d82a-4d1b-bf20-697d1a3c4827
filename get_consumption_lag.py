#!/usr/bin/env python3

from datetime import datetime
import json
import requests

URLlog = "https://advancedanalyticsapi.hexaglobe.com/log/"
URLuserLogin = "https://advancedanalyticsapi.hexaglobe.com/user/login"

def login_to_API1():

    PARAMS = {'username': '####', 'password': '####'}
    HEADERS = {'accept': 'application/json'}
    r = requests.post(url=URLuserLogin, headers=HEADERS, data=PARAMS)
    data = r.text
    return data

def get_last_consume_timestamp(token):

    PARAMS = "Request=(select%20__time%20from%20%22websocket-analytics-session-event%22%20where%20__time%20%3E%20CURRENT_TIMESTAMP%20-%20INTERVAL%20%2724%27%20HOUR%20order%20by%20__time%20DESC%20limit%201)"
    HEADERS = {"accept": "application/json", "Authorization": token, "Content-Type": "application/x-www-form-urlencoded"}
    r = requests.post(url=URLlog, headers=HEADERS, data=PARAMS)
    data = r.text
    return data

token = json.loads(login_to_API1())["Authorization"]
result = json.loads(get_last_consume_timestamp(token))

now = datetime.utcnow()
consume_datetime = datetime.strptime(result['data'][1][0], "%Y-%m-%dT%H:%M:%S.%fZ")
print((now - consume_datetime).total_seconds())
