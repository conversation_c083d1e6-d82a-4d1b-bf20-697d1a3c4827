import sys
import json
import logging
import geoip2.database
from logs_parser import LogsPars<PERSON>
from send_kafka.send_kafka import SendKafka
from confluent_kafka import Consumer
from hexaparser.hexa_parser import HexaParser
from datetime import datetime, timedelta
import maxminddb


def main(args: list) -> None:

    # Read the config file
    try:
        with open(args[0]) as file:
            config = json.load(file)
    except Exception as e:
        logging.error(f"WSParser: Cannot launch parser: wrong configuration file : {e}")

    # Define the databases
    reader_ip = geoip2.database.Reader(config.get("databases").get("reader_ip"))
    reader_ipco = geoip2.database.Reader(config.get("databases").get("reader_ipco"))
    reader_ipasn = geoip2.database.Reader(config.get("databases").get("reader_ipasn"))
    operator_strategies_filename = config.get("databases").get("operator_strategies")
    operator_strategies = maxminddb.open_database(operator_strategies_filename)
    cdn_ip_correspondances = json.load(
        open(config.get("databases").get("cdn_ip_correspondances"))
    )

    day = datetime.now() - timedelta(days=1)
    reader_ua = HexaParser(
        config.get("databases").get("reader_ua")
        + "/day"
        + str(day.isoweekday())
        + "/udgerdb_v3.dat"
    )
    # Define the confluent-kafka-python kafkaConsumer
    c = Consumer(
        {
            "bootstrap.servers": config.get("websocket_server_host"),
            "group.id": "toto_group",
            "auto.offset.reset": "latest",
        }
    )
    c.subscribe([config.get("websocket_server_topic")])

    send_kafka = SendKafka(config)
    parser = LogsParser(send_kafka)
    wsparser_version = config.get("wsparser_version")
    # Main loop that sends logs to parser
    count_to_update_databases = 0
    while True:
        msg = c.poll(1.0)
        if msg is None:
            continue
        if msg.error():
            print("Consumer error: {}".format(msg.error()))
            continue
        count_to_update_databases += 1
        if count_to_update_databases > 100000000:
            count_to_update_databases = 0
            print("RELOAD DB", flush=True)

            reader_ip = geoip2.database.Reader(config.get("databases").get("reader_ip"))
            reader_ipco = geoip2.database.Reader(
                config.get("databases").get("reader_ipco")
            )

            reader_ipasn = geoip2.database.Reader(
                config.get("databases").get("reader_ipasn")
            )

            reader_ua.update_db_filepath(
                config.get("databases").get("reader_ua")
                + "/day"
                + str(day.isoweekday())
                + "/udgerdb_v3.dat"
            )

            cdn_ip_correspondances = json.load(
                open(config.get("databases").get("cdn_ip_correspondances"))
            )

        if msg is not None:
            message = json.loads(msg.value().decode("utf-8"))
            if message["type"] == "UpdateStrategies":
                try:
                    print("RELOAD STRATEGY DB", flush=True)
                    operator_strategies_filename = (
                        config.get("databases").get("operator_strategies_dir")
                        + message["filename"]
                    )
                    operator_strategies = maxminddb.open_database(
                        operator_strategies_filename
                    )
                except Exception as e:
                    print("Error while reloading strategy DB" + str(e), message, flush=True)
            else:
                message["wsparser_version"] = wsparser_version
                try:
                    parser.logs_parser(
                        message,
                        reader_ip,
                        reader_ipco,
                        reader_ipasn,
                        reader_ua,
                        cdn_ip_correspondances,
                        operator_strategies,
                    )
                except Exception as e:
                    print("Error while parsing", message, flush=True)
                    print(e, flush=True)
                    pass


if __name__ == "__main__":
    main(sys.argv[1:])
