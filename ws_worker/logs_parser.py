from tables.session_table import SessionTable
from tables.stream_table import StreamTable
from tables.stream_quality_table import StreamQualityTable
from tables.event_table import EventTable
from tables.stream_heartbeat_table import StreamHeartbeatTable
from uri_regex_parser import <PERSON><PERSON><PERSON><PERSON><PERSON>
from expiringdict import ExpiringDict
import datetime


class LogsParser:
    """
    This class parses and stores the sessions informations before sending it to kafka
    """

    def __init__(self, send_kafka) -> None:
        self.no_watchtime_list = [
            "Pause",
            "Buffering",
            "Ended",
            "Error",
            "HexaPlayerErrorEvent",
            "HexaPlayerGeoBlockingErrorEvent",
        ]
        self.no_watchtime_list_but_buffer = [
            "Pause",
            "Ended",
            "Error",
            "HexaPlayerErrorEvent",
            "HexaPlayerGeoBlockingErrorEvent",
        ]
        self.watchtime_list = ["Play"]
        self.watchtime_list_after_buffering = [
            "DownloadHeadersReceived",
            "DownloadBodyReceived",
        ]
        self.last_delete_dead_sessions = datetime.datetime.now()
        self.sessions = {}
        self.sessions_streams = {}
        self.sessions_streams_quality = {}
        self.sessions_streams_heartbeat = {}
        self.sessions_last_event_time = {}
        self.watchtime_sessions_datetime = {}
        self.watchtime_sessions_duration = {}
        self.sessions_last_type_event = {}
        self.sessions_started = {}
        self.send_kafka = send_kafka
        self.tracker = None
        RegexChecker.compile_patterns()
        self.cache_dict = ExpiringDict(max_len=30000, max_age_seconds=10800)
        self.following_cache_size_iterator = 0

    def logs_parser(
        self,
        message: dict,
        reader_ip,
        reader_ipco,
        reader_ipasn,
        reader_ua,
        cdn_ip_correspondances,
        operator_strategies,
    ) -> None:
        # GLOBAL TREATMENT
        session_id = message["session_id"]
        if message["type"] == "Play":
            self.sessions_started[session_id] = True
        if (
            session_id in self.sessions and self.sessions[session_id].sent is False
        ) or (session_id not in self.sessions):
            self.sessions_last_event_time[session_id] = message["datetime"]
            self.sessions_last_type_event[session_id] = message["type"]
            # If new connection - create it in the sessions dict AND define all its metadatas
            if session_id not in self.sessions:

                if (
                    datetime.datetime.now() - self.last_delete_dead_sessions
                    > datetime.timedelta(seconds=60)
                ):
                    print(
                        "LAG IS : ",
                        (
                            float(datetime.datetime.now().timestamp() * 1000)
                            - float(message["datetime"])
                        )
                        / 1000,
                        "s and sessions number is : ",
                        len(self.sessions),
                        len(self.sessions_streams),
                        len(self.sessions_streams_quality),
                        len(self.sessions_streams_heartbeat),
                        len(self.sessions_last_event_time),
                        len(self.sessions_last_type_event),
                        len(self.watchtime_sessions_datetime),
                        len(self.watchtime_sessions_duration),
                        len(self.sessions_started),
                        flush=True,
                    )
                    self.last_delete_dead_sessions = datetime.datetime.now()
                    self.close_dead_sessions(message["datetime"])

                (
                    message["city_geoname_id"],
                    message["country_geoname_id"],
                    message["latitude"],
                    message["longitude"],
                    message["time_zone"],
                    message["postal_code"],
                    message["cidr"],
                    message["connection_type"],
                    message["asn"],
                    message["os_name"],
                    message["os_family"],
                    message["ua_name"],
                    message["ua_family"],
                    message["ua_family_vendor"],
                    message["device_type"],
                    message["browser_type"],
                    message["region_geoname_id"],
                    message["geoname_id"],
                ) = (
                    None,
                    None,
                    None,
                    None,
                    None,
                    None,
                    None,
                    None,
                    None,
                    None,
                    None,
                    None,
                    None,
                    None,
                    None,
                    None,
                    None,
                    None,
                )

                if self.following_cache_size_iterator > 100000:
                    print(
                        (
                            "Cache expiring dict geoip size :  "
                            + str(len(self.cache_dict))
                            + " / 30 000"
                        )
                    )
                    self.following_cache_size_iterator = 0
                self.following_cache_size_iterator += 1
                if self.cache_dict.get(message["ipAddress"]) is not None:
                    message.update(self.cache_dict.get(message["ipAddress"]))
                else:
                    new = {}
                    try:
                        result_ip = reader_ip.city(message["ipAddress"])
                        new["city_geoname_id"] = result_ip.city.name
                        new["country_geoname_id"] = result_ip.country.iso_code
                        new["latitude"] = result_ip.location.latitude
                        new["longitude"] = result_ip.location.longitude
                        new["time_zone"] = result_ip.location.time_zone
                        new["postal_code"] = result_ip.postal.code
                        new["cidr"] = result_ip.traits.network
                        if len(result_ip.subdivisions) > 0:
                            new["region_geoname_id"] = result_ip.subdivisions[
                                0
                            ].iso_code
                            new["geoname_id"] = result_ip.subdivisions[0].geoname_id
                    except Exception as e:
                        print("Error while getting geoip Info" + str(e), flush=True)
                    try:
                        result_ipco = reader_ipco.connection_type(message["ipAddress"])
                        new["connection_type"] = result_ipco.connection_type
                    except Exception as e:
                        print("Error while getting geoipco Info" + str(e), flush=True)
                    try:
                        result_ipasn = reader_ipasn.asn(message["ipAddress"])
                        new["asn"] = result_ipasn.autonomous_system_number
                    except Exception as e:
                        print("Error while getting geoipasn Info" + str(e), flush=True)
                    try:
                        result_ua = reader_ua.parse(message["ua"])
                        new["os_name"] = result_ua.get("os_name")
                        new["os_family"] = result_ua.get("os_family")
                        new["ua_name"] = result_ua.get("ua_name")
                        new["ua_family"] = result_ua.get("ua_family")
                        new["device_type"] = result_ua.get("device_type")
                        new["browser_type"] = result_ua.get("browser_type")

                    except Exception as e:
                        print(
                            "Error while getting user_agent udger Info" + str(e),
                            flush=True,
                        )
                    message.update(new)
                    self.cache_dict[message["ipAddress"]] = new

                session = SessionTable(message)
                self.sessions[session_id] = session

            (
                _,
                self.sessions[session_id].client,
                _,
                _,
                _,
                _,
                _,
                _,
                _,
                _,
                _,
                _,
                _,
                _,
                _,
                _,
            ) = RegexChecker.check_uri(message["media_url"])
            if self.sessions[session_id].client is not None:
                self.sessions[session_id].sent = True
                # send to kafka - start global session
                self.send_kafka.send_session_global(self.sessions[session_id].to_json)
                self.sessions[session_id].nb_flux = 1

                # STREAM TREATMENT
                stream = self.parse_stream_and_heartbeat(message)
                # EVENT TREATMENT
                self.parse_event(
                    message, stream, cdn_ip_correspondances, operator_strategies
                )
                # QUALITY TREATMENT
                self.parse_quality(message, stream)
        else:
            self.sessions_last_event_time[session_id] = message["datetime"]
            if message["type"] in self.no_watchtime_list and (
                session_id in self.sessions_last_type_event
                and self.sessions_last_type_event[session_id]
                not in self.no_watchtime_list_but_buffer
            ):
                self.sessions_last_type_event[session_id] = message["type"]

            # STREAM TREATMENT
            stream = self.parse_stream_and_heartbeat(message)
            # EVENT TREATMENT
            self.parse_event(
                message, stream, cdn_ip_correspondances, operator_strategies
            )
            # QUALITY TREATMENT
            self.parse_quality(message, stream)

        if message["type"] == "Disconnect":
            if session_id in self.sessions and self.sessions[session_id].sent is True:
                # If session is over and already exists delete it and send it to kafka
                self.disconnect_global(session_id)
                if session_id in self.sessions_last_event_time:
                    del self.sessions_last_event_time[session_id]
            elif (
                session_id in self.sessions and self.sessions[session_id].sent is False
            ):  # Session has been connected then nothing then disconnect
                self.sessions[session_id].is_empty = True
                self.disconnect_global(session_id)

    def parse_stream_and_heartbeat(self, message: dict):

        session_id = message["session_id"]
        message["video_title"], message["document_title"] = None, None
        (
            message["uri"],
            _,
            message["service_id"],
            message["uri_regex"],
            message["urlrgx_stream_protocol"],
            message["urlrgx_drm_type"],
            message["urlrgx_playlist_id"],
            message["urlrgx_stream_name"],
            message["urlrgx_partner_id"],
            message["language_stream"],
            message["urlrgx_server_name"],
            message["urlrgx_extension"],
            _,
            _,
            _,
            _,
        ) = RegexChecker.check_uri(message["media_url"])
        if session_id not in self.sessions_streams:
            if (
                (
                    message["type"] == "DownloadHeadersReceived"
                    or message["type"] == "VideoMetaData"
                )
                and "videoMetadata" in message
                and message["videoMetadata"] is not None
            ):
                if (
                    "videoTitle" in message["videoMetadata"]
                    and message["videoMetadata"]["videoTitle"] is not None
                ):
                    message["video_title"] = message["videoMetadata"]["videoTitle"]
                    message["document_title"] = message["videoMetadata"][
                        "documentTitle"
                    ]
        date = datetime.datetime.fromtimestamp(int(message["datetime"]) / 1000)
        message["minute"] = date.minute
        stream = StreamTable(message)
        stream.add_session_global(self.sessions[session_id])

        # Update stream ip if user changed ip
        if message["ipAddress"] is not None:
            stream.ip = message["ipAddress"]

        heartbeat = StreamHeartbeatTable(message)
        heartbeat.add_session_stream(stream)

        if session_id not in self.sessions_streams:
            if message["uri"] != "" and message["uri"] is not None:
                self.sessions_streams[session_id] = stream
                # send stream start log to kafka
                self.send_kafka.send_session_stream(stream.to_json)
                self.sessions_streams_heartbeat[session_id] = heartbeat
                heartbeat.start_sec = date.second
                # send stream HEARTBEAT start log to kafka
                self.send_kafka.send_session_stream_heartbeat(heartbeat.to_json)

                # If first event is a play set firstplay after sending stream start because it will override it when closing the stream
                if message["type"] == "Play":
                    stream.firstplay = message["datetime"]
                    heartbeat.add_session_stream(stream)
                    self.sessions_streams[session_id] = stream
                    self.sessions_streams_heartbeat[session_id] = heartbeat

        else:
            # If it is the first play of the session update firstplay and update heartbeat using stream
            if (
                message["type"] == "Play"
                and self.sessions_streams[session_id].firstplay == 0
            ):
                self.sessions_streams[session_id].firstplay = message["datetime"]
                self.sessions_streams_heartbeat[session_id].add_session_stream(
                    self.sessions_streams[session_id]
                )

            # FOR EACH NEW ALREADY EXISTING SESSION LOGS UPDATE START_EVENT IN HEARTBEAT
            # AND CHECK IF IT IS STILL THE SAME MINUTE AS THE PREVIOUS EVENT
            # OTHERWISE CREATE A NEW EVENT AT THE ROUND MINUTE AND SEND IT
            if session_id in self.sessions_streams_heartbeat:
                actual_heartbeat = self.sessions_streams_heartbeat[session_id]
                if actual_heartbeat.minute != date.minute:
                    tmp_date = date.replace(second=0, microsecond=0)
                    heartbeat_video_duration = actual_heartbeat.video_duration
                    # send previous event with end sec and end event to close it in druid w/ rollup
                    actual_heartbeat.end_sec = 60
                    actual_heartbeat.end_event = datetime.datetime.timestamp(tmp_date)
                    self.send_kafka.send_session_stream_heartbeat(
                        actual_heartbeat.to_json
                    )
                    # send next event with round minute for heartbeat with start sec and start event
                    actual_heartbeat.connected_since = (
                        actual_heartbeat.connected_since
                        + (actual_heartbeat.end_sec - actual_heartbeat.start_sec)
                    )
                    actual_heartbeat.minute = date.minute
                    actual_heartbeat.start_sec = 0
                    actual_heartbeat.start_event = str(
                        datetime.datetime.timestamp(tmp_date) * 1000
                    ).split(".")[0]
                    actual_heartbeat.end_sec = None
                    actual_heartbeat.end_event = None
                    actual_heartbeat.video_duration = 0
                    tmp_firstplay = actual_heartbeat.firstplay
                    tmp_error_buffering = actual_heartbeat.error_buffering
                    tmp_duration_rebuffering = actual_heartbeat.duration_rebuffering
                    actual_heartbeat.firstplay = 0  # set firstplay to 0 to not override it when sending heartbeat end
                    actual_heartbeat.error_buffering = 0  # set error_buffering to 0 to not sum it when sending heartbeat end
                    actual_heartbeat.duration_rebuffering = 0  # set duration_rebuffering to 0 to not sum it when sending heartbeat end
                    self.send_kafka.send_session_stream_heartbeat(
                        actual_heartbeat.to_json
                    )
                    actual_heartbeat.firstplay = tmp_firstplay
                    actual_heartbeat.error_buffering = tmp_error_buffering
                    actual_heartbeat.duration_rebuffering = tmp_duration_rebuffering
                    actual_heartbeat.video_duration = heartbeat_video_duration
                    self.sessions_streams_heartbeat[session_id] = actual_heartbeat

            self.sessions_streams[session_id].nblogs += 1
            if message["type"] == "Playback":
                self.sessions_streams[session_id].video_duration = message["duration"]
                self.sessions_streams_heartbeat[session_id].video_duration = message[
                    "duration"
                ]

            # Calculate cumulative watchtime
            if (
                (
                    (message["type"] in self.watchtime_list)
                    or (
                        session_id in self.sessions_last_type_event
                        and self.sessions_last_type_event[session_id] == "Buffering"
                        and message["type"] in self.watchtime_list_after_buffering
                    )
                )
                and session_id not in self.watchtime_sessions_datetime
                and session_id in self.sessions_started
            ):
                self.sessions_last_type_event[session_id] = "Play"
                self.watchtime_sessions_datetime[session_id] = message["datetime"]
                if session_id not in self.watchtime_sessions_duration:
                    self.watchtime_sessions_duration[session_id] = 0
            elif message["type"] in self.no_watchtime_list and session_id in list(
                self.watchtime_sessions_datetime
            ):
                self.watchtime_sessions_duration[session_id] += (
                    int(message["datetime"])
                    - int(self.watchtime_sessions_datetime[session_id])
                ) / 1000
                del self.watchtime_sessions_datetime[session_id]

            current_stream = self.sessions_streams[session_id]
            if (
                message["uri"] != current_stream.video_name
                and message["uri"] != ""
                and message["uri"] is not None
            ):
                if message["type"] != "Play":
                    self.sessions_last_type_event[session_id] = "Pause"
                stream.stream_id = current_stream.stream_id + 1
                self.sessions[session_id].nb_flux += 1
                heartbeat.stream_id = (
                    self.sessions_streams_heartbeat[session_id].stream_id + 1
                )

                if (
                    (
                        message["type"] == "DownloadHeadersReceived"
                        or message["type"] == "VideoMetaData"
                    )
                    and "videoMetadata" in message
                    and message["videoMetadata"] is not None
                ):
                    if (
                        "videoTitle" in message["videoMetadata"]
                        and message["videoMetadata"]["videoTitle"] is not None
                    ):
                        stream.video_title = message["videoMetadata"]["videoTitle"]
                        stream.document_title = message["videoMetadata"][
                            "documentTitle"
                        ]
                        heartbeat.video_title = message["videoMetadata"]["videoTitle"]
                        heartbeat.document_title = message["videoMetadata"][
                            "documentTitle"
                        ]
                else:
                    stream.video_title = ""
                    stream.document_title = ""
                    heartbeat.video_title = ""
                    heartbeat.document_title = ""

                end_stream = current_stream.to_json
                end_stream["session_stream_stop"] = message["datetime"]
                end_stream["session_stream_duration"] = (
                    int(message["datetime"]) - int(current_stream.session_stream_start)
                ) / 1000

                # Calculate cumulative watchtime when stream ends
                if session_id in list(self.watchtime_sessions_datetime):
                    self.watchtime_sessions_duration[session_id] += (
                        int(message["datetime"])
                        - int(self.watchtime_sessions_datetime[session_id])
                    ) / 1000
                    del self.watchtime_sessions_datetime[session_id]
                if session_id in list(self.watchtime_sessions_duration):
                    end_stream["cumulative_watchtime"] = (
                        self.watchtime_sessions_duration[session_id]
                    )
                    del self.watchtime_sessions_duration[session_id]
                else:
                    end_stream["cumulative_watchtime"] = 0

                # send previous stream end log to KAFKA
                self.send_kafka.send_session_stream(end_stream)
                # update le stream en cours
                tmp_firstplay = end_stream["firstplay"]
                stream.firstplay = 0  # set firstplay to 0 to not count it twice when sending stream end after
                stream.nb_errors = 0  # set errors back to 0
                self.sessions_streams[session_id] = stream
                # SEND LE NOUVEAU STREAM START !!!!
                self.send_kafka.send_session_stream(stream.to_json)
                self.sessions_streams[session_id].firstplay = tmp_firstplay

                end_heartbeat = self.sessions_streams_heartbeat[session_id].to_json
                end_heartbeat["session_stream_stop"] = message["datetime"]
                end_heartbeat["end_event"] = message["datetime"]
                end_heartbeat["end_sec"] = date.second
                end_heartbeat["session_stream_duration"] = (
                    int(message["datetime"])
                    - int(
                        self.sessions_streams_heartbeat[session_id].session_stream_start
                    )
                ) / 1000
                # send previous stream HEARTBEAT end log to KAFKA
                self.send_kafka.send_session_stream_heartbeat(end_heartbeat)

                # update le stream HEARTBEAT en cours
                heartbeat.connected_since = end_heartbeat["connected_since"] + (
                    end_heartbeat["end_sec"] - end_heartbeat["start_sec"]
                )
                self.sessions_streams_heartbeat[session_id] = heartbeat
                heartbeat.start_sec = date.second
                heartbeat.video_duration = 0
                heartbeat.firstplay = 0  # set firstplay to 0 to not count it twice when sending stream end after
                self.sessions_streams_heartbeat[session_id] = heartbeat
                # SEND LE NOUVEAU STREAM HEARTBEAT START !!!!
                self.send_kafka.send_session_stream_heartbeat(heartbeat.to_json)
                heartbeat.add_session_stream(self.sessions_streams[session_id])
                self.sessions_streams_heartbeat[session_id] = heartbeat

            # if (message["video_title"] and message["uri"]):
            #    print("update stream video metadatas", flush=True)
            # update le stream en cours
            # self.sessions_streams[session_id].video_title = message["video_title"]
            # self.sessions_streams[session_id].document_title = message["document_title"]
            # update le stream HEARTBEAT en cours
            # self.sessions_streams_heartbeat[session_id].video_title = message["video_title"]
            # self.sessions_streams_heartbeat[session_id].document_title = message["document_title"]

        # DANS CETTE CONDITION AJOUTER LE NOUVEL EVENT STOP STREAM
        if message["type"] == "Disconnect" and session_id in self.sessions_streams:
            self.disconnect_stream(session_id)

        if (
            session_id in self.sessions_streams
            and self.sessions_streams[session_id].video_duration
            and self.sessions_streams[session_id].video_duration > 0
        ):
            stream.video_duration = self.sessions_streams[session_id].video_duration
        return stream

    def parse_event(
        self,
        message: dict,
        stream: StreamTable,
        cdn_ip_correspondances,
        operator_strategies,
    ):
        (
            message["segment"],
            message["segment_pattern"],
            message["segment_extension"],
            message["segment_type"],
            message["segment_quality"],
            message["segment_duration"],
            message["segment_creation_date"],
            message["segment_delay_between_req_and_file_gen"],
        ) = (None, None, None, None, None, None, None, None)
        if "downloaded_body_received_url" in message:
            (
                message["segment"],
                _,
                _,
                message["segment_pattern"],
                _,
                _,
                _,
                _,
                _,
                _,
                _,
                message["segment_extension"],
                message["segment_type"],
                message["segment_quality"],
                message["segment_duration"],
                message["segment_creation_date"],
            ) = RegexChecker.check_uri(message["downloaded_body_received_url"])
        if message["segment_creation_date"]:
            message["segment_delay_between_req_and_file_gen"] = (
                int(message["datetime"]) - int(message["segment_creation_date"])
            ) / 1000
        if message["type"] == "Error" or message["type"] == "HexaPlayerErrorEvent":
            self.sessions[message["session_id"]].nb_errors += 1
            self.sessions_streams[message["session_id"]].nb_errors += 1

        # Count rebuffering duration for stream and heartbeat
        if message["session_id"] in self.sessions_streams:
            if (
                message["type"] != "Buffering"
                and self.sessions_streams[
                    message["session_id"]
                ].last_event_is_rebuffering
                is True
                and self.sessions_streams[message["session_id"]].had_first_buffering
                is True
            ):
                self.sessions_streams[message["session_id"]].duration_rebuffering += (
                    int(message["datetime"])
                    - int(
                        self.sessions_streams[
                            message["session_id"]
                        ].last_rebuffering_time
                    )
                ) / 1000
                self.sessions_streams_heartbeat[
                    message["session_id"]
                ].duration_rebuffering += (
                    int(message["datetime"])
                    - int(
                        self.sessions_streams[
                            message["session_id"]
                        ].last_rebuffering_time
                    )
                ) / 1000
                self.sessions_streams[message["session_id"]].last_rebuffering_time = 0
            # Count rebuffering duration for quality
            if (
                message["type"] != "Buffering"
                and self.sessions_streams[
                    message["session_id"]
                ].last_event_is_rebuffering
                is True
                and self.sessions_streams_quality[
                    message["session_id"]
                ].had_first_buffering
                is True
            ):
                self.sessions_streams_quality[
                    message["session_id"]
                ].duration_rebuffering += (
                    int(message["datetime"])
                    - int(
                        self.sessions_streams_quality[
                            message["session_id"]
                        ].last_rebuffering_time
                    )
                ) / 1000
                self.sessions_streams_quality[
                    message["session_id"]
                ].last_rebuffering_time = 0

        if message["type"] == "Buffering" and stream.service_id is not None:
            if (
                self.sessions_streams[message["session_id"]].last_event_is_rebuffering
                is not True
            ):
                self.sessions_streams[
                    message["session_id"]
                ].last_event_is_rebuffering = True
                self.sessions_streams[message["session_id"]].last_rebuffering_time = (
                    message["datetime"]
                )
                if message["session_id"] in self.sessions_streams_quality:
                    self.sessions_streams_quality[
                        message["session_id"]
                    ].last_rebuffering_time = message["datetime"]

            if (
                message["session_id"] in self.sessions_streams_quality
                and self.sessions_streams_quality[
                    message["session_id"]
                ].had_first_buffer
                is False
            ):
                self.sessions_streams_quality[
                    message["session_id"]
                ].had_first_buffer = True
            elif message["session_id"] in self.sessions_streams_quality:
                self.sessions_streams_quality[
                    message["session_id"]
                ].had_first_buffering = True

            if self.sessions_streams[message["session_id"]].had_first_buffer is False:
                self.sessions_streams[message["session_id"]].had_first_buffer = True
            else:
                message["is_rebuffering"] = True
                self.sessions_streams[message["session_id"]].had_first_buffering = True
                self.sessions_streams[message["session_id"]].error_buffering += 1
                self.sessions_streams_heartbeat[
                    message["session_id"]
                ].error_buffering += 1
                if message["session_id"] in self.sessions_streams_quality:
                    self.sessions_streams_quality[
                        message["session_id"]
                    ].error_buffering += 1
        elif message["session_id"] in self.sessions_streams:
            self.sessions_streams[message["session_id"]].last_event_is_rebuffering = (
                False
            )

        event = EventTable(message, cdn_ip_correspondances, operator_strategies)
        event.add_session_stream(stream)

        # Update user ip if user changed ip
        if message["ipAddress"] is not None:
            event.ip = message["ipAddress"]

        # Send event to kafka
        self.send_kafka.send_event(event.to_json)

    def parse_quality(self, message: dict, stream: StreamTable):
        message_datetime = message["datetime"]
        (
            message["segment"],
            message["segment_pattern"],
            message["segment_extension"],
            message["segment_type"],
            message["segment_quality"],
            message["segment_duration"],
            message["segment_creation_date"],
            message["segment_delay_between_req_and_file_gen"],
            message["bandwidth"],
            message["bitrate"],
            message["height"],
            message["level"],
            message["width"],
        ) = (
            None,
            None,
            None,
            None,
            None,
            None,
            None,
            None,
            None,
            None,
            None,
            None,
            None,
        )
        if "downloaded_body_received_url" in message:
            (
                message["segment"],
                _,
                _,
                message["segment_pattern"],
                _,
                _,
                _,
                _,
                _,
                _,
                _,
                message["segment_extension"],
                message["segment_type"],
                message["segment_quality"],
                message["segment_duration"],
                message["segment_creation_date"],
            ) = RegexChecker.check_uri(message["downloaded_body_received_url"])
        if message["segment_creation_date"]:
            message["segment_delay_between_req_and_file_gen"] = (
                int(message_datetime) - int(message["segment_creation_date"])
            ) / 1000
        quality = StreamQualityTable(message)
        quality.add_session_stream(stream)
        session_id = message["session_id"]
        if session_id not in self.sessions_streams_quality:
            if message["uri"] != "" and message["uri"] is not None:
                self.sessions_streams_quality[session_id] = quality
                # send stream quality start log to kafka
                self.send_kafka.send_session_stream_quality(quality.to_json)
            # If first event is a play set firstplay after sending stream start because it will override it when closing the stream
            if message["type"] == "Play":
                quality.add_session_stream(stream)
                self.sessions_streams_quality[session_id] = quality
        else:
            # If it is the first play of the session update firstplay and update heartbeat using stream
            if (
                message["type"] == "Play"
                and self.sessions_streams_quality[session_id].firstplay == 0
            ):
                self.sessions_streams_quality[session_id].firstplay = message[
                    "datetime"
                ]

            current_stream = self.sessions_streams_quality[session_id]
            if (
                message["uri"]
                and message["uri"] != current_stream.video_name
                and message["uri"] != ""
            ):
                end_stream_quality = current_stream.to_json
                end_stream_quality["end_event"] = message_datetime
                end_stream_quality["event_duration"] = (
                    int(message_datetime) - int(current_stream.session_stream_start)
                ) / 1000
                # send previous stream end log to KAFKA
                self.send_kafka.send_session_stream_quality(end_stream_quality)
                # update le stream en cours
                quality.stream_id = current_stream.stream_id + 1
                self.sessions_streams_quality[session_id] = quality
                quality.firstplay = 0  # set firstplay to 0 because it's a new stream
                # SEND LE NOUVEAU STREAM START !!!!
                self.send_kafka.send_session_stream_quality(quality.to_json)
                quality.add_session_stream(stream)  # set back the firstplay

            if message["type"] == "Bitrate":
                end_stream_quality = current_stream.to_json
                end_stream_quality["end_event"] = message_datetime
                end_stream_quality["event_duration"] = (
                    int(message_datetime) - int(current_stream.session_stream_start)
                ) / 1000
                # send previous stream end log to KAFKA
                self.send_kafka.send_session_stream_quality(end_stream_quality)
                # update le stream en cours
                quality.quality_id = current_stream.quality_id + 1
                quality.stream_id = current_stream.stream_id
                self.sessions_streams_quality[session_id] = quality
                quality.firstplay = 0  # set firstplay to 0 because it's a new stream
                # send next stream start log to KAFKA
                self.send_kafka.send_session_stream_quality(quality.to_json)
                quality.firstplay = current_stream.firstplay  # set back the firstplay
                self.sessions_streams_quality[session_id] = (
                    quality  # set back the firstplay
                )

            # DANS CETTE CONDITION AJOUTER LE NOUVEL EVENT STOP STREAM
            elif (
                message["type"] == "Disconnect"
                and session_id in self.sessions_streams_quality
            ):
                self.disconnect_stream_quality(session_id)

    def close_dead_sessions(self, last_event_time: int):
        threshold = float(last_event_time) - 70000
        for session_id in list(self.sessions_last_event_time):
            if float(self.sessions_last_event_time[session_id]) < threshold:
                self.disconnect_global(session_id)
                self.disconnect_stream(session_id)
                self.disconnect_stream_quality(session_id)
                if session_id in self.sessions_last_event_time:
                    del self.sessions_last_event_time[session_id]
                if session_id in self.sessions_last_type_event:
                    del self.sessions_last_type_event[session_id]
                if session_id in self.sessions_started:
                    del self.sessions_started[session_id]

    def disconnect_global(self, session_id):
        if session_id in self.sessions:
            # Delete global session
            session = self.sessions[session_id]
            if not session.websocket_connection_time:
                session.websocket_connection_time = int(
                    datetime.datetime.now().timestamp() * 1000
                )
            if session_id in self.sessions_last_event_time:
                session.websocket_deconnection_time = self.sessions_last_event_time[
                    session_id
                ]
                session.total_connection_duration = (
                    int(self.sessions_last_event_time[session_id])
                    - int(session.websocket_connection_time)
                ) / 1000
            else:
                session.websocket_deconnection_time = int(
                    datetime.datetime.now().timestamp() * 1000
                )
                session.total_connection_duration = (
                    int(datetime.datetime.now().timestamp() * 1000)
                    - int(session.websocket_connection_time)
                ) / 1000
            # send to kafka - end global session
            self.send_kafka.send_session_global(session.to_json)
            del self.sessions[session_id]

    def disconnect_stream(self, session_id):
        if session_id in self.sessions_streams:
            # Delete stream session
            last_event_time = self.sessions_last_event_time[session_id]
            session_stream_duration = (
                int(last_event_time)
                - int(self.sessions_streams[session_id].session_stream_start)
            ) / 1000
            end_last_stream = self.sessions_streams[session_id].to_json
            end_last_stream["session_stream_stop"] = last_event_time
            end_last_stream["session_stream_duration"] = session_stream_duration
            end_last_stream["websocket_deconnection_time"] = int(last_event_time)

            # Deal with cumulative watchtime
            if session_id in list(self.watchtime_sessions_datetime):
                self.watchtime_sessions_duration[session_id] += (
                    int(last_event_time)
                    - int(self.watchtime_sessions_datetime[session_id])
                ) / 1000
                del self.watchtime_sessions_datetime[session_id]
            if session_id in list(self.watchtime_sessions_duration):
                end_last_stream["cumulative_watchtime"] = (
                    self.watchtime_sessions_duration[session_id]
                )
                del self.watchtime_sessions_duration[session_id]
            else:
                end_last_stream["cumulative_watchtime"] = 0

            # send previous stream end log to KAFKA
            self.send_kafka.send_session_stream(end_last_stream)
            del self.sessions_streams[session_id]
            # send stream HEARTBEAT end log to KAFKA
            heartbeat = self.sessions_streams_heartbeat[session_id]
            heartbeat.end_sec = datetime.datetime.fromtimestamp(
                int(last_event_time) / 1000
            ).second
            heartbeat.end_event = int(last_event_time)
            heartbeat.session_stream_stop = int(last_event_time)
            heartbeat.websocket_deconnection_time = int(last_event_time)
            heartbeat.session_stream_duration = session_stream_duration
            self.send_kafka.send_session_stream_heartbeat(heartbeat.to_json)
            del self.sessions_streams_heartbeat[session_id]
            if session_id in self.sessions_started:
                del self.sessions_started[session_id]
            if session_id in self.sessions_last_type_event:
                del self.sessions_last_type_event[session_id]

    def disconnect_stream_quality(self, session_id):
        if session_id in self.sessions_streams_quality:
            # Delete stream quality session
            end_last_stream_quality = self.sessions_streams_quality[session_id].to_json
            end_last_stream_quality["end_event"] = self.sessions_last_event_time[
                session_id
            ]
            end_last_stream_quality["event_duration"] = (
                int(end_last_stream_quality["end_event"])
                - int(end_last_stream_quality["session_stream_start"])
            ) / 1000
            # send previous stream quality end log to KAFKA
            self.send_kafka.send_session_stream_quality(end_last_stream_quality)
            del self.sessions_streams_quality[session_id]
