from .session_table import SessionTable


class StreamTable:
    """
    This table save the information about a session.
    """

    def __init__(self, stream):

        self.last_event_is_rebuffering = False
        self.last_rebuffering_time = 0
        self.video_duration = 0
        self.cumulative_watchtime = 0
        self.firstplay = 0
        self.nb_errors = 0
        self.video_name_cut = None
        self.regex = None
        # self.client = None
        self.service_id = stream["service_id"]
        # self.tracker_in_url = None
        # self.content_name_stream = None

        self.nblogs = 1
        self.stream_id = 1
        self.session_stream_duration = 0
        self.websocket_deconnection_time = 0
        self.session_stream_stop = None
        self.session_stream_start = stream["datetime"]
        self.parent_id = stream["parentId"]
        # self.video_name = stream["name"]
        self.video_name = stream["uri"]
        self.language_stream = stream["language_stream"]
        self.video_id = stream["videoId"]
        self.had_first_buffer = False
        self.had_first_buffering = False
        self.error_buffering = 0
        self.duration_rebuffering = 0

        self.player_version = stream["player_version"]
        self.player_type = stream["player_type"]
        self.client_log_program = stream["client_log_program"]
        self.client_log_program_version = stream["client_log_program_version"]
        self.client_log_plugin = stream["client_log_plugin"]
        self.client_log_plugin_version = stream["client_log_plugin_version"]
        self.hexa_shaka_player_version = stream["hexa_shaka_player_version"]
        self.uri = stream["uri"]
        self.playHead = stream["playHead"]
        self.playRate = stream["playRate"]
        self.droppedFrames = stream["droppedFrames"]
        self.live = stream["live"]

        if self.live == "True":
            self.type_content = "live"
        else:
            self.type_content = "vod"

        self.uri_regex = stream["uri_regex"]
        self.urlrgx_stream_protocol = stream["urlrgx_stream_protocol"]
        self.urlrgx_drm_type = stream["urlrgx_drm_type"]
        self.urlrgx_playlist_id = stream["urlrgx_playlist_id"]
        self.urlrgx_stream_name = stream["urlrgx_stream_name"]
        self.urlrgx_partner_id = stream["urlrgx_partner_id"]
        self.urlrgx_extension = stream["urlrgx_extension"]
        self.urlrgx_server_name = stream["urlrgx_server_name"]
        self.video_title = stream["video_title"]
        self.document_title = stream["document_title"]

    def add_session_global(self, session: SessionTable) -> None:
        self.__dict__.update(session.to_json)

    @property
    def to_json(self) -> dict:
        return self.__dict__.copy()
