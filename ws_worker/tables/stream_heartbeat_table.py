from .stream_table import StreamTable


class StreamHeartbeatTable:
    """
    This table save the events by minute.
    """

    def __init__(self, heartbeat):
        # self.bandwidth = None
        # self.bitrate = None
        # self.height = None
        # self.level = None
        # self.width = None

        self.start_sec = None
        self.end_sec = None
        self.minute = heartbeat["minute"]
        self.start_event = heartbeat["datetime"]
        self.end_event = None
        self.stream_id = 1
        # self.type = None
        self.event_duration = None
        self.is_rebuffering = None
        self.index_buffering = None
        self.websocket_deconnection_time = 0
        self.connected_since = 0
        # self.segment = heartbeat["segment"]
        # self.segment_extension = heartbeat["segment_extension"]
        # self.segment_pattern = heartbeat["segment_pattern"]
        # self.segment_type = heartbeat["segment_type"]
        # self.segment_quality = heartbeat["segment_quality"]
        # self.segment_duration = heartbeat["segment_duration"]
        # self.segment_creation_date = heartbeat["segment_creation_date"]
        # self.segment_delay_between_req_and_file_gen = heartbeat["segment_delay_between_req_and_file_gen"]

        # PAR CONTRE ON PEUT AJOUTER LES INFOS URI REGEX COMME STREAM

        # self.errorCode = None
        # self.errorMessage = None
        # self.geoBlockingIpAddressError = None

    def add_session_stream(self, stream: StreamTable) -> None:
        self.__dict__.update(stream.to_json)

    @property
    def to_json(self) -> dict:
        return self.__dict__.copy()
