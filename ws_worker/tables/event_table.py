from .stream_table import StreamTable
import struct
import socket


class EventTable:
    """
    This table save the information received in a log.
    """

    def __init__(self, event, cdn_ip_correspondances: dict, operator_strategies):

        self.operators_correspondance = {
            "https://248-speed-cdn.hexaglobe.net/": "TH2-EQUINIX",
            "https://249-speed-cdn.hexaglobe.net/": "TH2-FRANCEIX",
            "https://250-speed-cdn.hexaglobe.net/": "TH2-COGENT",
            "https://251-speed-cdn.hexaglobe.net/": "TH2-HOPUS",
            "https://252-speed-cdn.hexaglobe.net/": "TH2-LUMEN",
            "https://253-speed-cdn.hexaglobe.net/": "PA3-ARELION",
        }

        self.operators_correspondance_strategy = {
            28: "TH2-EQUINIX",
            29: "TH2-FRANCEIX",
            30: "TH2-COGENT",
            31: "TH2-HOPUS",
            32: "TH2-LUMEN",
            33: "PA3-ARELION",
        }

        self.datetime = event["datetime"]
        self.type = event["type"]
        self.connected = event["connected"]
        self.value = event["value"]
        self.segment = event["segment"]
        self.segment_extension = event["segment_extension"]
        self.segment_pattern = event["segment_pattern"]
        self.segment_type = event["segment_type"]
        self.segment_quality = event["segment_quality"]
        self.segment_duration = event["segment_duration"]
        self.segment_creation_date = event["segment_creation_date"]
        self.segment_delay_between_req_and_file_gen = event[
            "segment_delay_between_req_and_file_gen"
        ]
        self.errorCode = event["errorCode"]
        self.errorMessage = event["errorMessage"]
        self.geoBlockingIpAddressError = None
        if "is_rebuffering" in event and event["is_rebuffering"] is not None:
            self.is_rebuffering = event["is_rebuffering"]
        else:
            self.is_rebuffering = False
        if event["type"] == "HexaPlayerGeoBlockingErrorEvent":
            self.geoBlockingIpAddressError = event["ipAddress"]

        self.contentLength = event["contentLength"]
        self.timeMs = event["timeMs"]
        self.bitrate = None

        if self.timeMs and self.timeMs != 0 and self.contentLength:
            # User downloading bitrate in bit/s
            try:
                self.bitrate = int((int(self.contentLength) * 8) / (self.timeMs / 1000))
            except Exception:
                pass

        self.iteration = event["iteration"]
        self.operator = None
        try:
            if event["segment"]:
                if (
                    event["segment"].rsplit("/", 1)[0] + "/"
                ) in self.operators_correspondance:
                    self.operator = self.operators_correspondance[
                        event["segment"].rsplit("/", 1)[0] + "/"
                    ]
        except Exception as e:
            print(f"Error in parsing of uri with error{e} : ", event["segment"], flush=True)

        self.operators_correspondance = None

        self.ip_source = None
        try:
            self.ip_source = cdn_ip_correspondances[event["urlrgx_server_name"]]
        except Exception:
            pass

        self.test = None
        if self.operator is None:
            try:
                strategies = operator_strategies.get(event["ipAddress"])
                self.operator = self.operators_correspondance_strategy[
                    strategies[self.ip_source][
                        (struct.unpack("!I", socket.inet_aton(event["ipAddress"]))[0])
                        % 16
                    ]
                ]
            except Exception:
                pass
                # print("Error while getting operator_strategies Info", flush=True)

    def add_session_stream(self, stream: StreamTable) -> None:
        self.__dict__.update(stream.to_json)

    @property
    def to_json(self) -> dict:
        return self.__dict__.copy()
