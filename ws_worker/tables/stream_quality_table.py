from .stream_table import StreamTable


class StreamQualityTable:
    """
    This table save the information when there is a change in the stream quality.
    """

    def __init__(self, quality):

        if (
            quality["value"] != ""
            and quality["value"] is not None
            and "bandwidth" in quality["value"]
            and "bitrate" in quality["value"]
            and "height" in quality["value"]
            and "level" in quality["value"]
            and "width" in quality["value"]
        ):
            self.bandwidth = quality["value"]["bandwidth"]
            self.bitrate = quality["value"]["bitrate"]
            self.height = quality["value"]["height"]
            self.level = quality["value"]["level"]
            self.width = quality["value"]["width"]

        self.stream_id = 1
        self.quality_id = 1

        self.had_first_buffer = False
        self.had_first_buffering = False
        self.last_rebuffering_time = 0
        self.duration_rebuffering = 0

        self.start_event = quality["datetime"]
        self.end_event = None
        self.event_duration = None
        self.is_rebuffering = None
        self.segment = quality["segment"]
        self.segment_extension = quality["segment_extension"]
        self.segment_pattern = quality["segment_pattern"]
        self.segment_type = quality["segment_type"]
        self.segment_quality = quality["segment_quality"]
        self.segment_duration = quality["segment_duration"]
        self.segment_creation_date = quality["segment_creation_date"]
        self.segment_delay_between_req_and_file_gen = quality[
            "segment_delay_between_req_and_file_gen"
        ]
        self.errorCode = quality["errorCode"]
        self.errorMessage = quality["errorMessage"]
        self.geoBlockingIpAddressError = None
        if quality["type"] == "HexaPlayerGeoBlockingErrorEvent":
            self.geoBlockingIpAddressError = quality["ipAddress"]

    def add_session_stream(self, stream: StreamTable) -> None:
        self.__dict__.update(stream.to_json)

    @property
    def to_json(self) -> dict:
        return self.__dict__.copy()
