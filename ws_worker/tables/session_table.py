import struct
import socket


class SessionTable:
    """
    This table save the global information of a connection.
    """

    def __init__(self, session):

        self.sent = False
        self.is_empty = "false"

        # User Agent
        self.os_name = session["os_name"]
        self.os_family = session["os_family"]
        self.ua_name = session["ua_name"]
        self.ua_family_vendor = session["ua_family_vendor"]
        self.ua_family = session["ua_family"]
        self.device_type = session["device_type"]
        self.browser_type = session["browser_type"]

        # Geoip
        self.city_geoname_id = session["city_geoname_id"]
        self.region_geoname_id = session["region_geoname_id"]
        self.country_geoname_id = session["country_geoname_id"]
        self.latitude = session["latitude"]
        self.longitude = session["longitude"]
        self.time_zone = session["time_zone"]
        self.postal_code = session["postal_code"]
        self.geoname_id = session["geoname_id"]
        self.connection_type = session["connection_type"]
        self.asn = session["asn"]
        self.ip = session["ipAddress"]
        self.ip_modulo = (struct.unpack("!I", socket.inet_aton(self.ip))[0]) % 16

        # User/Session
        self.user_id = session["user_id"]
        self.session_id = session["session_id"]
        self.nb_flux = 0
        self.nb_errors = 0
        self.client = None

        # Settings
        self.orientation = session["orientation"]
        self.screen_height = session["screenheight"]
        self.screen_width = session["screenwidth"]
        self.pixel_density = session["pixelDensity"]
        self.referrer = session["referrer"]
        self.retina_display = session["retinaDisplay"]
        self.cidr = session["cidr"]

        # Info
        self.url_page = session["current_page_url"]
        self.websocket_connection_time = session["datetime"]
        self.websocket_deconnection_time = 0
        self.total_connection_duration = 0
        self.timezone_offset = session["timezoneoffset"]

        # Versions
        # self.customer_name = None
        self.wsserver_version = session["wsserver_version"]
        self.wsserver_name = session["wsserver_name"]
        self.wsparser_version = session["wsparser_version"]
        if "httpserver_version" in session:
            self.httpserver_version = session["httpserver_version"]
        else:
            self.httpserver_version = None

    @property
    def to_json(self) -> dict:
        return self.__dict__.copy()
