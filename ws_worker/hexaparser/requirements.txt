#
# This file is autogenerated by pip-compile with Python 3.12
# by the following command:
#
#    pip-compile --output-file=requirements.txt requirements.in
#
expiringdict==1.2.2
    # via -r requirements.in
numpy==1.26.1
    # via
    #   -r requirements.in
    #   pandas
pandas==2.1.1
    # via -r requirements.in
python-dateutil==2.8.2
    # via
    #   -r requirements.in
    #   pandas
pytz==2023.3.post1
    # via
    #   -r requirements.in
    #   pandas
redis==5.0.1
    # via -r requirements.in
six==1.16.0
    # via
    #   -r requirements.in
    #   python-dateutil
tzdata==2023.3
    # via
    #   -r requirements.in
    #   pandas
