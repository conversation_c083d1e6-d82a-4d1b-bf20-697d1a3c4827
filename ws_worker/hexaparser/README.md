# Hexaparser

## 1. External dependencies. 

In order to run this project, you must have a working installation of Python 3.6 or higher and install the libraiies listed in the requirement.txt file.

To install the libraries, you can use the following command:

```shell
# Inside a virtual environment (pyenv or poetry not recommended)
pip install -r requirements.txt
```

To update the dependencies, meddle with the `requirements.in` and run:

```shell
# Install pip-tools if needed
pip install pip-tools

# Compile the requirements
pip-compile requirements.in --output-file requirements.txt
```

If you want to use a redis database as a caching system, you will also need to install redis. 

For this project to run, you will also need a working and up to date udger database. It is strongly recommanded to keep this database up to date using the udger update script. Moreover one should create two folder in order to not read in a database that is being written in. For more detail on how to download the udgerdb_v3.dat file, please contact <PERSON><PERSON><PERSON> ENTRAYGUES <<EMAIL>> or anyone from the analytic team at Hexaglobe.

## 2. How to run the project

Once your python environment is set up, You should check the config.ini file to make it match what you want for your project.

Here is a description of the different parameters:

- Custom_parsing_enabled: True *Can take the value True or False. If set to True, the project will read the custom_parsing.csv file and add its content to the caching system. If set to False, the project will not read the custom_parsing.csv file and will not add its content to the caching system.*
- Size_of_expiring_dict: 10000 *Can take any integer value. This is the size of the expiring dictionnary that will be used to cache the parsing of the user agent. If the size of the dictionnary is reached, the oldest entry will be deleted.*
- Memory_time_of_expiring_dict: 18000 *Can take any integer value. This is the time in seconds that an entry will stay in the expiring dictionnary.*
- Is_reddis_enabled: True *Can take the value True or False. If set to True, the project will use a redis database as a caching system in addition of expiring dictionnary. If set to False, the project will not use it.*
- Redis_host: localhost *Can take any string value. This is the host of the redis database.*
- Redis_port: 6379 *Can take any integer value. This is the port of the redis database.*
- Redis_memory_time : 86400 *Can take any integer value. This is the time in seconds that an entry will stay in the redis database.*


you can run the project by first calling the HexaParser class with the path to the udger_db by importing the library as shown below:
```python
from hexa_parser import HexaParser
db_filepath = "path/to/udgerdb_v3.dat"
parser = HexaParser(db_filepath)
```
After that you can parse any user_agent by calling the parse function of the HexaParser class as shown below:
```python
from hexa_parser import HexaParser
db_filepath = "path/to/udgerdb_v3.dat"
parser = HexaParser(db_filepath)
x = "user_agent you want to parse"
parser.parse(x)
```
The function returns a dictionnary with the following keys:
- "ua" that gives the family of the browser 
- "ua_version" that gives the version of the browser
- "browser_type" that gives the type of the browser
- "os" that gives the family of the operating system
- "os_version" that gives the version of the operating system
- "device" that gives the family of the device
- "crawler" that gives the family of the crawler like bot or webscraper

The exemple_run.py file gives an exemple of how to run the project on minimalistic condition. 

In order to update the db_filepath or the custom caching information in the custom_parsing.csv file, you can use the following functions:

```python
from hexa_parser import HexaParser
db_filepath = "path/to/udgerdb_v3.dat"
parser = HexaParser(db_filepath)
parser.update_db_filepath("new/path/to/udgerdb_v3.dat")
parser.update_custom_parsing()
```

## 3. Futher advice on how to use the project

To processe a large volume of data it is recommanded to use a caching system. For a project that will run for a long time a library like [redis](https://redis.io/) or the library [expiringdict](https://github.com/mailgun/expiringdict). For a project that will run for a short time, it is recommanded to just use a dictionnary.

An exemple of this type of implementation can be found in kafkalogfilter repository.

Beside usefull functions are available in the HexaParser class. Here is a description of the different functions:

- update_db_filepath: This function takes a string as an argument and updates the db_filepath attribute of the HexaParser class.

- update_custom_parsing: This function takes no argument and updates the cache with the custom_parsing.csv file.

- reset_cache: This function takes no argument and resets the cache of the HexaParser class.