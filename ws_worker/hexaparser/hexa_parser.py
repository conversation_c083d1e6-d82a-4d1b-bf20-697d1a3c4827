# import libraries
import os
import pandas as pd

try:
    import redis
except ImportError:
    pass
try:
    from expiringdict import ExpiringDict
except ImportError:
    pass
try:
    from .crawler import (
        load_data,
        get_crawler,
        get_device,
        get_os,
        get_browser,
        get_cache,
    )
except ImportError:
    from crawler import (
        load_data,
        get_crawler,
        get_device,
        get_os,
        get_browser,
        get_cache,
    )
try:
    from .words import create_word_detector, extract_words_from_table
except ImportError:
    from words import create_word_detector, extract_words_from_table
import configparser
from threading import Lock
import logging


class HexaParser:

    def __init__(self, db_filepath):
        self._lock = Lock()
        self.db_filepath = db_filepath
        self.logger = self.set_logger()
        self.caching()

        # 1. Create a ConfigParser object
        config = configparser.ConfigParser()

        # 2. Read the configuration file
        self.logger.info("Reading the configuration file")

        try:
            config.read(os.path.dirname(__file__) + "/config.ini")

            # 3. Access the values using section and option names
            hexa_parser_config = config["HEXA_PARSER_CONFIG"]
            self.custom_parsing_enabled = hexa_parser_config.getboolean(
                "Custom_parsing_enabled"
            )
            self.size_of_expiring_dict = hexa_parser_config.getint(
                "Size_of_expiring_dict"
            )
            self.memory_time_of_expiring_dict = hexa_parser_config.getint(
                "Memory_time_of_expiring_dict"
            )
            self.is_redis_enabled = hexa_parser_config.getboolean("Is_reddis_enabled")
            if self.is_redis_enabled:
                redis_host = hexa_parser_config.get("Redis_host")
                redis_port = hexa_parser_config.getint("Redis_port")
                self.redis_memory_time = hexa_parser_config.getint("Redis_memory_time")

        except Exception as e:
            self.logger.error(f"Error while reading the configuration file {e}")
            raise e

        # 4. Create the cache and the redis db
        self.logger.info("Creating the cache")
        try:
            self.cache_dict = ExpiringDict(
                max_len=self.size_of_expiring_dict,
                max_age_seconds=self.memory_time_of_expiring_dict,
            )
        except Exception as e:
            error_message = (
                "ExpiringDict could not be created with param size_of_expiring_dict = "
                f"{self.size_of_expiring_dict} and memory_time_of_expiring_dict = "
                f"{self.memory_time_of_expiring_dict}"
            )
            self.logger.error(error_message)
            self.logger.error(e)
            self.logger.warning("We will use a dictionary instead of ExpiringDict")
            self.cache_dict = {}

        if self.is_redis_enabled:
            self.logger.info("Creating the redis db")
            try:
                self.db = redis.StrictRedis(host=redis_host, port=redis_port, db=0)
                # self.db.flushall()
            except Exception as e:
                self.logger.error(
                    f"Redis connection failed with param redis_host = {redis_host} and redis_port = {redis_port}"
                )
                self.logger.error(e)
                self.logger.info("change the config.ini file to disable redis")
                raise e
        else:
            self.db = None

        # 5. Update the cache and the redis db with the custom parsing file
        if self.custom_parsing_enabled:
            self.logger.info("Updating the cache with the custom parsing file")
            try:
                df = pd.read_csv(
                    os.path.dirname(__file__) + "/custom_parsing.csv", header=0
                )
                df.fillna(str(""), inplace=True)
                for index, row in df.iterrows():
                    ans = {
                        "ua": row["Browser"],
                        "ua_version": row["Browser Version"],
                        "browser_type": row["Browser type"],
                        "os": row["OS"],
                        "os_version": row["OS Version"],
                        "device": row["Device"],
                        "crawler": row["Crawler"],
                    }
                    self.cache_dict[row["User_agent"]] = ans

                    if self.is_redis_enabled:
                        self.logger.info(
                            "Updating the redis db with the custom parsing file"
                        )
                        self.db.set(row["User_agent"], self.To_string(ans))
                        self.db.expire(row["User_agent"], 100000000000000)

            except Exception as e:
                self.logger.error(
                    "Custom parsing failed check that the custom_parsing.csv file is accessible"
                )
                self.logger.error(e)
                self.logger.info("we will not use the custom parsing file")

    # gets necessary caches from the database
    def caching(self):
        # get the cache for the ip, the words and the regex
        # id_to_class
        (
            self.class_to_browsertype,
            self.id_to_device,
            self.id_to_os,
            self.id_to_browser,
            self.crawler_to_class,
            self.cache_ip
        ) = load_data(self.db_filepath)

        # device
        self.device_words_dict = create_word_detector(
            self.db_filepath, "udger_deviceclass_regex", "udger_deviceclass_regex_words"
        )
        self.device_regex_words = extract_words_from_table(
            self.db_filepath, "udger_deviceclass_regex", "deviceclass_id"
        )

        # browser
        self.browser_words_dict = create_word_detector(
            self.db_filepath, "udger_client_regex", "udger_client_regex_words"
        )
        self.browser_regex_words = extract_words_from_table(
            self.db_filepath, "udger_client_regex", "client_id"
        )

        # os
        self.os_words_dict = create_word_detector(
            self.db_filepath, "udger_os_regex", "udger_os_regex_words"
        )
        self.os_regex_words = extract_words_from_table(
            self.db_filepath, "udger_os_regex", "os_id"
        )

    # turn the parsed ua into a string
    def To_string(self, parsed_ua: dict) -> str:
        line = ""
        for key, value in parsed_ua.items():
            line += f"{key}\t{value}\t"
        # remove the last \t
        line = line[:-1]
        return line

    # In order to update the filepath of the database
    def update_db_filepath(self, db_filepath: str):
        if db_filepath == self.db_filepath:
            self.logger.info(f"db_filepath is already {db_filepath}")
            return
        self.logger.info(f"Updating the db_filepath to {db_filepath}")
        try:
            self.db_filepath = db_filepath
            self.caching()
        except Exception as e:
            self.logger.error(f"Error while updating the db_filepath to {db_filepath}")
            self.logger.error(e)

    # In order to update the cache and the redis db with the custom parsing file
    def update_custom_parsing(self):
        if self.custom_parsing_enabled:
            try:
                self.logger.info("Updating the cache with the custom parsing file")
                df = pd.read_csv(
                    os.path.dirname(__file__) + "/custom_parsing.csv", header=0
                )
                df.fillna("", inplace=True)
                for index, row in df.iterrows():
                    ans = {
                        "ua": row["Browser"],
                        "ua_version": row["Browser Version"],
                        "browser_type": row["Browser type"],
                        "os": row["OS"],
                        "os_version": row["OS Version"],
                        "device": row["Device"],
                        "crawler": row["Crawler"],
                    }
                    self.cache_dict[row["User_agent"]] = ans

                    if self.is_redis_enabled:
                        self.db.set(row["User_agent"], self.To_string(ans))
                        self.db.expire(row["User_agent"], 100000000000000)

            except Exception as e:
                self.logger.error(
                    "Custom parsing failed check that the custom_parsing.csv file is accessible"
                )
                self.logger.error(e)

        else:
            self.logger.info("Custom parsing is not enabled cannot update")

    # turn the string ua into a dict
    def To_dict(self, string_ua: str) -> dict:
        dict_ua = {}
        string_ua = string_ua.split("\t")
        for x in range(0, len(string_ua) - 1, 2):
            dict_ua[string_ua[x]] = string_ua[x + 1]
        return dict_ua

    # parse an ip
    def parse_ip(self, ip: str):
        return self.cache_ip.get(ip)

    # general_parsing
    def parse(self, user_agent: str) -> dict:
        # user_agent = parse_ua_for_injection(user_agent)
        try:
            with self._lock:
                # check if the user agent is in the cache
                if self.cache_dict.get(user_agent) is not None:
                    answer = self.cache_dict.get(user_agent)
                    self.cache_dict[user_agent] = answer

                # check if the user agent is in the redis db
                elif self.is_redis_enabled and self.db.get(user_agent) is not None:
                    self.db.expire(user_agent, self.redis_memory_time)
                    answer = self.To_dict(self.db.get(user_agent).decode("utf-8"))
                    self.cache_dict[user_agent] = answer

                # if not, parse it and add it to the redis db and the cache
                else:
                    answer = self.custom_parser(user_agent)
                    self.cache_dict[user_agent] = answer
                    if self.is_redis_enabled:
                        self.db.set(user_agent, self.To_string(answer))
                        self.db.expire(user_agent, self.redis_memory_time)

            return {
                "os_name": (
                    answer["os_version"] if answer["os_version"] != "" else answer["os"]
                ),
                "os_family": answer["os"],
                "ua_name": str(answer["ua"]) + str(answer["ua_version"]),
                "ua_family": answer["ua"],
                "device_type": answer["device"],
                "browser_type": answer["browser_type"],
                "crawler": answer["crawler"],
            }
        except Exception as e:
            self.logger.error(f"Error in parsing with user agent : {user_agent}")
            self.logger.error(e)
            return dict()

    # reset cache function
    def reset_cache(self):
        self.logger.info("Resetting the cache")
        if self.is_redis_enabled:
            self.db.flushdb()
        try:
            self.cache_dict = ExpiringDict(
                max_len=self.size_of_expiring_dict,
                max_age_seconds=self.memory_time_of_expiring_dict,
            )
        except Exception as e:
            self.cache_dict = {}
            logging.warning(
                "ExpiringDict could not be created with param size_of_expiring_dict = "
                + str(self.size_of_expiring_dict)
                + " and memory_time_of_expiring_dict = "
                + str(self.memory_time_of_expiring_dict)
                + " with error "
                + str(e)
            )
            
        # Should we call self.caching() here?
        self.cache_ip = get_cache(self.db_filepath, "udger_ip_list")

        if self.custom_parsing_enabled:
            self.logger.info("Updating the cache with the custom parsing file")
            try:
                df = pd.read_csv(
                    os.path.dirname(__file__) + "/custom_parsing.csv", header=0
                )
                df.fillna("", inplace=True)
                for index, row in df.iterrows():
                    ans = {
                        "ua": row["Browser"],
                        "ua_version": row["Browser Version"],
                        "browser_type": row["Browser type"],
                        "os": row["OS"],
                        "os_version": row["OS Version"],
                        "device": row["Device"],
                        "crawler": row["Crawler"],
                    }
                    self.cache_dict[row["User_agent"]] = ans

                    if self.is_redis_enabled:
                        self.db.set(row["User_agent"], self.To_string(ans))
                        self.db.expire(row["User_agent"], 100000000000000)

            except Exception as e:
                self.logger.error(
                    "Custom parsing failed check that the custom_parsing.csv file is accessible"
                )
                self.logger.error(e)

    # get all the information from the user agent
    def custom_parser(self, ua: str) -> dict:
        self.db_filepath
        class_to_browsertype = self.class_to_browsertype
        id_to_device = self.id_to_device
        # get information on the os, the family and the version
        try:
            # os,version_os = get_os(ua,db_filepath)
            os_family, os_name, version_os = get_os(
                ua,
                self.os_words_dict.find_words(ua),
                self.os_regex_words,
                self.id_to_os,
            )
            if len(os_family) != 0:
                os_family = os_family[0][0]
                os_name = os_name[0][0]
            else:
                os_family = "unrecognized"
                os_name = "unrecognized"
            if len(version_os) != 0:
                version_os = version_os[0]
            else:
                version_os = ""
        except Exception:
            os_family = "unrecognized"
            os_name = "unrecognized"
            version_os = ""

        # get the information on the browser, the family, the version and the type of the browser
        try:
            browser, browser_class, version_ua = get_browser(
                ua,
                self.browser_words_dict.find_words(ua),
                self.browser_regex_words,
                self.id_to_browser,
            )
            # the device guess comes from the browser type
            device_guess = None
            if len(browser) != 0:
                browser_type = class_to_browsertype[browser_class[0][0]][0]
                device_guess = class_to_browsertype[browser_class[0][0]][1]
                browser = browser[0][0]

            else:
                browser = "unrecognized"
                browser_type = "unrecognized"
                device_guess = None

            if len(version_ua) != 0:
                version_ua = version_ua[0]
            else:
                version_ua = ""

        except Exception:
            browser = "unrecognized"
            browser_type = "unrecognized"
            version_ua = ""
            device_guess = None

        # get the information on the device using the user_agent and the device guess
        try:
            device = get_device(
                ua,
                device_guess,
                id_to_device,
                self.device_words_dict.find_words(ua),
                self.device_regex_words,
            )[0][0]
        except Exception:
            device = "unrecognized"

        # get the information on the crawler
        try:
            crawler = get_crawler(ua, self.crawler_to_class)
        except Exception as e:
            print(e, flush=True)
            crawler = "unrecognized"

        return {
            "ua": browser,
            "ua_version": version_ua,
            "browser_type": browser_type,
            "os": os_family,
            "os_version": os_name + str(version_os),
            "device": device,
            "crawler": crawler,
        }

    def set_logger(self) -> logging.Logger:
        # Create a logger
        logger = logging.getLogger(__name__)

        # Configure the logger
        logger.setLevel(logging.DEBUG)  # Set the desired logging level

        # Create a console handler
        console_handler = logging.StreamHandler()

        # Create a formatter to format log messages
        formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )

        # Set the formatter for the handlers
        console_handler.setFormatter(formatter)

        # Add the console handler to log to the console
        logger.addHandler(console_handler)

        return logger
