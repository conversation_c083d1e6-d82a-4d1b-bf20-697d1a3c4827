# import libraries
from hexa_parser import HexaParser

# fix: block comment should start with '# '


def main():
    # show the path to the database
    db_filepath = "udger_db/udgerdb_v3.dat"
    # create the parser object
    parser = HexaParser(db_filepath)
    # parse all the user agents
    tab = [
        "Equidia",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "AppleCoreMedia/1.0.0.20G75 (iPhone; U; CPU OS 16_6 like Mac OS X; fr_fr)",
        "(Windows NT 10.0; Win64; x64) PotPlayer/23.12.20",
        "Africanews tvOS/1.2 (com.africanews; build:12; tvOS 14.4.0) Alamofire/4.8.2",
    ]
    for x in tab:
        parsed_ua = parser.parse(x)
        print(parsed_ua)


if __name__ == "__main__":
    main()
