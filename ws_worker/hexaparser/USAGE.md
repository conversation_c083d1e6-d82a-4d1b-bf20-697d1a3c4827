# Project architecture and usage

## Project structure
### Extracting and caching information from the udger database
First a system of cache is creating extracting the data from the udger database and storing it into different dictionaries.  

The first dictionaries are used to make basic transformation like id to class_string without using a database call everytime.  

The second dictionaries are the words dictionaries, those will store a list of 2 words and the related regex line. The idea of it is to reduce the number of regex to be tested by only testing the ones that have a chance to match. It is faster because matching the regex is way more time consuming then checking if a word is in the user agent.  

### Parsing the user agent 
There are two use cases for the user agent parsing. If the user agent is already in the cache, the function will return the result from the cache. If not, it will try to match the user agent with the regex.  

Those call are made in the get_{attribute} function of the crawler.py file.  
They will first check if the words extracted in the user agent are in the words of the candidate regex and then if the regex match the user agent. The function will then return a list of possible result the first one being the most probable one.

### How the user agent cache is working

Depending on your choices of architecture, the cache can be stored in different ways.  
The most common way is to store it in an expiring dictionary. It allows the user to have a cache of a limited size and to not keep in cache forever unused user agents.  
For smaller project you can also store it in a simple dictionary and clear it from time to time which is less efficient but easier to implement without any settings to change in the config.ini file.  
If your project is kind of big or if you want to keep memory between different runs of the program, you can also use a redis database to store the cache. You then just have to change the settings in the config.ini file to use the redis database.  

An other interesting way to store cache with this project that can be useful especially if you have several workers is to use both the expiring dictionary and the redis database. You can then store the cache in the redis database and use the expiring dictionary to store the most used user agents inside the workers. An exemple of this architecture is given in the kafkalogfilter project. 


If you have any question regarding this project feel free to contact :
- [Arthur wiriath](mailto:<EMAIL>)
- [Anthony COURCOUX](mailto:<EMAIL>)


