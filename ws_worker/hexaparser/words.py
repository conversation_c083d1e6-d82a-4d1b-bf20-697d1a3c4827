import sqlite3


class WordDetector(object):

    _wdict = {}

    def add_word(self, wid, word):
        prefix = word[:3]
        if prefix not in self._wdict:
            self._wdict[prefix] = [
                (wid, word),
            ]
        else:
            self._wdict[prefix].append(
                (wid, word),
            )

    def find_words(self, text):
        found_words = set()
        found_words.add(0)
        s = text.lower()
        for x in range(0, len(s) - 2):
            word_infos = self._wdict.get(s[x : x + 3], ())
            found_words.update(wi[0] for wi in word_infos if s.startswith(wi[1], x))
        return found_words


def dict_factory(cursor, row):
    return dict((col[0], row[idx]) for idx, col in enumerate(cursor.description))


# creates used words dictionnary
def create_word_detector(db_filepath, regex_table, word_table_name) -> WordDetector:
    db = sqlite3.connect(db_filepath)
    db_cursor = db.cursor()
    db_cursor.row_factory = dict_factory
    wdetector = WordDetector()

    # gets all words from the word_table that matches any of the two ids in the regex_table
    for row in db_cursor.execute(
        "SELECT * FROM %s WHERE id IN (SELECT word_id FROM %s) OR id IN (SELECT word2_id FROM %s)"
        % (word_table_name, regex_table, regex_table)
    ):
        wdetector.add_word(row["id"], row["word"])

    db.close()
    return wdetector


def extract_words_from_table(db_filepath, regex_table, type) -> list:
    row_id_words = []
    db = sqlite3.connect(db_filepath)
    db_cursor = db.cursor()
    db_cursor.row_factory = dict_factory
    sql = f"SELECT {type},regstring,word_id,word2_id FROM " + regex_table
    for row in db_cursor.execute(sql):
        if row["regstring"][0] == "/":
            row["regstring"] = row["regstring"][1:]
        row["regstring"] = row["regstring"].replace("/si", "")
        row_id_words.append(row)
    db.close()
    return row_id_words
