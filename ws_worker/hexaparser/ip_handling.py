import sqlite3


def get_ip_class(db_filepath: str, ip: str) -> list:
    # get the db
    db = sqlite3.connect(db_filepath)
    # add our custom function to sqlite
    c = db.cursor()
    table = "udger_ip_list"
    matched_string = ip
    # execute the query
    c.execute(
        f'SELECT ip_classification FROM udger_ip_class where id in (SELECT class_id FROM {table} WHERE ip = "{matched_string}")'
    )
    res = c.fetchmany(100)
    db.commit()
    db.close()
    return res


# #regex function to match a range of ip
# def regexp(ips, item):
#     try :
#         ip_from = ips[0]
#         ip_to = ips[1]
#         ips_from = ip_from.split(".")
#         ips_to = ip_to.split(".")
#         item = item.split(".")

#     except :
#         return False

#     try :
#         for i in range(0,4) :
#             if int(item[i]) < int(ips_from[i]) or int(item[i]) > int(ips_to[i]) :
#                 return False
#         return True
#     except :
#         return False

# def get_datacenter(ip,db_filepath) :
#     #get the db
#     db = sqlite3.connect(db_filepath)
#     #add our custom function to sqlite
#     db.create_function("REGEXP", 2, regexp)
#     c = db.cursor()
#     table = "udger_datacenter_range"
#     matched_string = ip
#     #execute the query
#     c.execute(f'SELECT name FROM udger_datacenter_list where id in (SELECT datacenter_id FROM {table} WHERE (ip_from,ip_to) REGEXP "{matched_string}")')
#     res = c.fetchmany(100)
#     db.commit()
#     db.close()
#     return res
