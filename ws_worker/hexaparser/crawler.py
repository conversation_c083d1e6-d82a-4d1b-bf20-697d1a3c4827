import sqlite3
import re


def get_os(ua: str, found_words: set, regexlist: list, id_to_os: dict) -> list:
    version_os = []
    res_family = []
    res_name = []
    # only look for regex that matches the words in the user agent
    filtered_regex = [
        r
        for r in regexlist
        if r["word_id"] in found_words and r["word2_id"] in found_words
    ]
    # for each regex we try to match the user agent and then fill the infos
    for r in filtered_regex:
        matche = re.search(r["regstring"], ua, re.IGNORECASE)
        if matche is not None:
            res_family.append([id_to_os[r["os_id"]][0]])
            res_name.append([id_to_os[r["os_id"]][1]])
            try:
                version_os.append(matche.group(1))
            except Exception:
                version_os.append("")

    return res_family, res_name, version_os


def get_device(
    ua: str, device_guess: int, id_to_device: dict, found_words: set, regexlist: list
) -> list:
    res = []
    # only look for regex that matches the words in the user agent
    filtered_regex = [
        r
        for r in regexlist
        if r["word_id"] in found_words and r["word2_id"] in found_words
    ]
    for r in filtered_regex:
        matche = re.search(r["regstring"], ua, re.IGNORECASE)
        if matche is not None:
            res.append([id_to_device[r["deviceclass_id"]]])
            break

    # if the regex do not match we guess using the browser_type
    if len(res) == 0 and device_guess is not None:
        res = [[id_to_device[device_guess]]]
    return res


def get_browser(
    ua: str, found_words: set, regexlist: list, id_to_browser: dict
) -> list:
    version_ua = []
    res_name = []
    res_class = []
    # only look for regex that matches the words in the user agent
    filtered_regex = [
        r
        for r in regexlist
        if r["word_id"] in found_words and r["word2_id"] in found_words
    ]
    # for each regex we try to match the user agent and then fill the infos
    for r in filtered_regex:
        matche = re.search(
            r["regstring"], ua, flags=re.IGNORECASE | re.MULTILINE | re.DOTALL
        )
        if matche is not None:
            res_name.append([id_to_browser[r["client_id"]][1]])
            res_class.append([id_to_browser[r["client_id"]][0]])
            try:
                version_ua.append(matche.group(1))
            except Exception:
                version_ua.append("")
    return res_name, res_class, version_ua


# function to know if the user_agent is a bot or type of crawler
def get_crawler(ua: str, crawler_to_class: dict):
    return crawler_to_class.get(ua)


# function to get the cache dictionnary from the sqlite db
def get_cache(db_filepath: str, option: str) -> dict:
    db = sqlite3.connect(db_filepath)
    c = db.cursor()
    match option:
        # get the cache for the ip classification
        case "udger_ip_list":
            c.execute(
                f"SELECT ip,ip_classification FROM {option} left join udger_ip_class on udger_ip_list.class_id = udger_ip_class.id;"
            )
            res = c.fetchall()
            res = {x[0]: x[1] for x in res}

        # get the cache for the browser type and the device type
        case "udger_client_class":
            c.execute(f"SELECT id,client_classification,deviceclass_id FROM {option}")
            res = c.fetchmany(100000000)
            res = {x[0]: [x[1], x[2]] for x in res}

        # get the cache for the name assiocated to the device class.
        case "udger_deviceclass_list":
            c.execute(f"SELECT id,name FROM {option}")
            res = c.fetchmany(100000000)
            res = dict(res)

        # get the cache for the os name and family
        case "udger_os_list":
            res = c.execute(f"SELECT id,family,name FROM {option}")
            res = {x[0]: (x[1], x[2]) for x in res}

        # get the cache for the browser name and class
        case "udger_client_list":
            res = c.execute(f"SELECT id,class_id,name FROM {option}")
            res = {x[0]: (x[1], x[2]) for x in res}

        # get the cache for the crawler name and class
        case "udger_crawler_list":
            res = c.execute(
                f"SELECT ua_string,crawler_classification FROM {option} left join udger_crawler_class on udger_crawler_list.class_id = udger_crawler_class.id;"
            )
            res = {x[0]: x[1] for x in res}

        # if the option is not in the list we return an empty dictionnary
        # needs to be decided
        case _:
            res = {}

    db.commit()
    db.close()
    return res


# allow to get the required dictionnaries.
def load_data(db_filepath: str) -> tuple:
    class_to_browsertype = get_cache(db_filepath, "udger_client_class")
    id_to_device = get_cache(db_filepath, "udger_deviceclass_list")
    id_to_os = get_cache(db_filepath, "udger_os_list")
    id_to_browser = get_cache(db_filepath, "udger_client_list")
    crawler_to_class = get_cache(db_filepath, "udger_crawler_list")
    cache_ip = get_cache(db_filepath, "udger_ip_list")
    return (
        class_to_browsertype,
        id_to_device,
        id_to_os,
        id_to_browser,
        crawler_to_class,
        cache_ip
    )
