FROM ubuntu:22.04
LABEL maintainer="<PERSON> <<EMAIL>>"

RUN apt-get update
RUN apt-get install -y gcc g++ python3.11 python3.11-dev python3.11-venv librdkafka-dev

ENV VIRTUAL_ENV=/opt/wsserver/venv
RUN python3.11 -m venv $VIRTUAL_ENV
ENV PATH="$VIRTUAL_ENV/bin:$PATH"

COPY . .

WORKDIR .

RUN pip install --upgrade pip
RUN pip install -r requirements.txt
RUN chmod +x main.py

CMD ["python", "-m", "main", "config/conf.json"]
