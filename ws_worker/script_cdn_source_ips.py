#!/usr/bin/env python3

import requests
import json
import urllib3
from dotenv import dotenv_values


# Importing credential from .env file in the same directory
config = dotenv_values("../.env")

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
URLuserLogin = "https://advancedanalyticsapi.hexaglobe.com/user/login"
URLlog = "https://advancedanalyticsapi.hexaglobe.com/log/"


def login_to_API1():

    PARAMS = {"username": config["USER"], "password": config["PASS"]}
    HEADERS = {"accept": "application/json"}
    r = requests.post(url=URLuserLogin, headers=HEADERS, data=PARAMS, verify=False)
    data = r.text
    return data


def get_websocket_hosts(token):
    PARAMS = "Request=select%20%22urlrgx_server_name%22%20from%20%22websocket-analytics-session-event%22%20where%20%20__time%20%3E%20CURRENT_DATE%20-%20INTERVAL%20%271%27%20DAY%20group%20by%20urlrgx_server_name"
    HEADERS = {
        "accept": "application/json",
        "Authorization": token,
        "Content-Type": "application/x-www-form-urlencoded",
    }
    r = requests.post(url=URLlog, headers=HEADERS, data=PARAMS, verify=False)
    data = r.text
    return data


def get_nginx_correspondance_hosts(token, hosts):
    PARAMS = (
        "Request=select%20host%2C%20fromhost_ip%20from%20%22all-filtered-nginx-log-entries-Xm%22%20where%20__time%20%3E%20CURRENT_DATE%20-%20INTERVAL%20%271%27%20DAY%20and%20host%20in%20"
        + hosts
        + "%20group%20by%20%20%22fromhost_ip%22%2C%20host"
    )
    HEADERS = {
        "accept": "application/json",
        "Authorization": token,
        "Content-Type": "application/x-www-form-urlencoded",
    }
    r = requests.post(url=URLlog, headers=HEADERS, data=PARAMS, verify=False)
    data = r.text
    return data


TOKEN1 = json.loads(login_to_API1())["Authorization"]
result = json.loads(get_websocket_hosts(TOKEN1))["data"]
result.pop(0)

hosts = ""
for elem in result:
    hosts = hosts + "'" + elem[0] + "'" + ","

hosts = (
    "(" + hosts[:-1][3:] + ")"
)  # remove last comma and add parenthesis for where statement
result_nginx = json.loads(get_nginx_correspondance_hosts(TOKEN1, hosts))["data"]
result_nginx.pop(0)

json_data = {item[0]: item[1] for item in result_nginx}

with open("./databases/cdn_ip_source_correspondances.json", "w") as json_file:
    json_file.write(json.dumps(json_data))
