{"databases": {"reader_ua": "./databases/", "reader_ip": "./databases/GeoIP2-City.mmdb", "reader_ipco": "./databases/GeoIP2-Connection-Type.mmdb", "reader_ipasn": "./databases/GeoLite2-ASN.mmdb", "cdn_ip_correspondances": "./databases/cdn_ip_source_correspondances.json", "operator_strategies": "./databases/nns/dboperator.mmdb", "operator_strategies_dir": "./databases/nns/"}, "topic_global": "websocket-analytics-session-global", "topic_stream": "websocket-analytics-session-stream", "topic_stream_heartbeat": "websocket-analytics-session-heartbeat", "topic_stream_quality": "websocket-analytics-session_quality", "topic_event": "websocket-analytics-session_event", "kafka_host": "************:9092", "websocket_server_host": "************:9092", "websocket_server_topic": "websocket-analytics-downloadbody", "wsparser_version": "1.0.0"}