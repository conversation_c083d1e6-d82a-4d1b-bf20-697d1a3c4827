#
# This file is autogenerated by pip-compile with Python 3.11
# by the following command:
#
#    pip-compile --output-file=requirements.txt requirements.in
#
aiohttp==3.9.5
    # via geoip2
aiosignal==1.3.1
    # via aiohttp
attrs==23.2.0
    # via aiohttp
certifi==2024.7.4
    # via requests
charset-normalizer==3.3.2
    # via requests
confluent-kafka==2.0.2
    # via -r requirements.in
expiringdict==1.2.2
    # via -r requirements.in
frozenlist==1.4.1
    # via
    #   aiohttp
    #   aiosignal
geoip2==4.8.0
    # via -r requirements.in
idna==3.7
    # via
    #   requests
    #   yarl
kazoo==2.5.0
    # via pykafka
maxminddb==2.5.1
    # via
    #   -r requirements.in
    #   geoip2
multidict==6.0.5
    # via
    #   aiohttp
    #   yarl
numpy==2.0.0
    # via pandas
pandas==2.2.2
    # via -r requirements.in
pykafka==2.8.0
    # via -r requirements.in
python-dateutil==2.9.0.post0
    # via pandas
pytz==2024.1
    # via pandas
requests==2.32.3
    # via geoip2
six==1.16.0
    # via
    #   kazoo
    #   pykafka
    #   python-dateutil
tabulate==0.9.0
    # via pykafka
tzdata==2024.1
    # via pandas
ujson==5.7.0
    # via -r requirements.in
urllib3==2.2.2
    # via requests
yarl==1.9.4
    # via aiohttp

# The following packages are considered to be unsafe in a requirements file:
# setuptools
