from pykafka import KafkaClient
from queue import Empty
import ujson


class SendKafka():

    def __init__(self, kafka: dict):
        host_kafka = kafka.get("kafka_host")
        topic_session_global = kafka.get("topic_global")
        topic_session_stream = kafka.get("topic_stream")
        topic_event = kafka.get("topic_event")
        topic_session_quality = kafka.get("topic_stream_quality")
        topic_session_heartbeat = kafka.get("topic_stream_heartbeat")

        client_kafka = KafkaClient(hosts=host_kafka)
        set_producer = \
            lambda x: client_kafka.topics[x.encode("utf-8")].get_producer(use_rdkafka=True, delivery_reports=False, max_queued_messages=1000000)

        self._producer_session_global = set_producer(topic_session_global)
        self._producer_session_stream = set_producer(topic_session_stream)
        self._producer_event = set_producer(topic_event)
        self._producer_session_quality = set_producer(topic_session_quality)
        self._producer_session_heartbeat = set_producer(topic_session_heartbeat)

        self._product = \
            lambda x, producer: producer.produce(ujson.dumps(x, default=str, ensure_ascii=False).encode('utf-8'))

    def send_session_global(self, table: dict) -> None:
        self._product(table, self._producer_session_global)

    def send_session_stream(self, table: dict) -> None:
        self._product(table, self._producer_session_stream)

    def send_event(self, table: dict) -> None:
        self._product(table, self._producer_event)

    def send_session_stream_quality(self, table: dict) -> None:
        self._product(table, self._producer_session_quality)

    def send_session_stream_heartbeat(self, table: dict) -> None:
        self._product(table, self._producer_session_heartbeat)

    def _get_delivery_report(self, producer):
        while True:
            try:
                msg, exc = producer.get_delivery_report(block=False)
                if exc is not None:
                    print('Failed to deliver msg {}: {}'.format(msg.partition_key, repr(exc)))
                #else:
                #    print('Successfully delivered msg {}'.format(msg.partition_key))
            except Empty:
                break