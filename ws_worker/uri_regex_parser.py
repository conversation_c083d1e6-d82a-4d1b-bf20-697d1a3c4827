import re


class RegexChecker:

    _EXTENSIONS = [
        "m3u8-0",
        "m3u8-0BIS",
        "m3u8-1",
        "m3u8-2",
        "m3u8-3",
        "m3u8-4",
        "m3u8-5",
        "mpd",
        "mpd-1",
        "m4s-0",
        "m4s-1",
        "m4s-2",
        "m4s-3",
        "m4s-4",
        "m4s-5",
        "m4s-6",
        "ts-0",
        "ts-0BIS",
        "ts-1",
        "ts-2",
        "ts-3",
        "ts-4",
        "ts-5",
        "ts-6",
        "ts-7",
        "ts-8",
        "ts-9",
        "ts-10",
        "mp4-0",
        "mp4",
        "mp4-2",
    ]

    _PATTERNS = {
        "m3u8-0": "^(?P<https>[a-z:]*)\/\/(?P<host>[a-zA-Z0-9-.]*)\/(?P<secure_token>\w+)\/(?P<secure_time_token>\w+)\/(?P<client>\w+)\/(?P<partner_id>[a-zA-Z0-9-]*)\/(?P<servicepriabr>[a-zA-Z0-9]*)_(?P<language_bis>\w+).smil\/(?P<slot>\w+)_audio_(?P<language>\w+).m3u8",  # Equidia NEW
        "m3u8-0BIS": "^(?P<https>[a-z:]*)\/\/(?P<host>[a-zA-Z0-9-.]*)\/(?P<secure_token>\w+)\/(?P<secure_time_token>\w+)\/(?P<client>\w+)\/(?P<partner_id>[a-zA-Z0-9-]*)\/(?P<servicepriabr>[a-zA-Z0-9]*)_(?P<language>\w+).smil\/(?P<slot>\w+)_(?P<quality>\w+).m3u8",  # Equidia NEW
        "m3u8-1": "^(?P<https>[a-z:]*)\/\/(?P<host>[a-zA-Z0-9-.]*)\/dl\/(?P<secure_token>\w+)\/(?P<secure_time_token>\w+)\/(?P<ovp_service>[a-zA-Z0-9-_]*)\/videos\/hls\/(?P<h1>\w+)\/(?P<h2>\w+)\/(?P<S_playlist_id>\w+)\/(?P<V_playlist_id>\w+)\/playlist.m3u8",  # Equidia
        "m3u8-2": "^(?P<https>[a-z:]*)\/\/(?P<host>[a-zA-Z0-9-.]*)\/dl\/(?P<secure_token>\w+)\/(?P<secure_time_token>\w+)\/(?P<ovp_service>[a-zA-Z0-9-_]*)\/hls\/(?P<h1>\w+)\/(?P<h2>\w+)\/(?P<playlist_id>\w+)\/playlist.m3u8",  # Equidia
        "m3u8-3": "^(?P<https>[a-z:]*)\/\/(?P<host>[a-zA-Z0-9-.]*)\/(?P<secure_token>\w+)\/(?P<secure_time_token>\w+)\/(?P<client>\w+)\/(?P<partner_id>[a-zA-Z0-9-]*)\/(?P<servicepriabr>[a-zA-Z0-9]*)_(?P<language>\w+).smil\/(?P<DRM_type>\w+).m3u8",  # Equidia
        "m3u8-4": "^(?P<https>[a-z:]*)\/\/(?P<host>[a-zA-Z0-9-.]*)\/(?P<VERSION>[a-zA-Z0-9-]*)\/(?P<KEY1>[a-zA-Z0-9-]*)\/(?P<KEY2>[a-zA-Z0-9-_]*)\/(?P<video_id>[a-zA-Z0-9-]*).ism\/manifest.m3u8",  # Universcine
        "m3u8-5": "^(?P<https>[a-z:]*)\/\/(?P<host>[a-zA-Z0-9-.]*)\/(?P<VERSION>[a-zA-Z0-9-]*)\/(?P<KEY1>[a-zA-Z0-9-]*)\/(?P<KEY2>[a-zA-Z0-9-_]*)\/(?P<video_id>[a-zA-Z0-9-]*).ism\/(?P<video_id1>[a-zA-Z0-9-]*)-audio_(?P<language>\w+)=(?P<qualityaudio>\w+)-video=(?P<quality>\w+).m3u8",  # Universcine
        "mpd": "^(?P<https>[a-z:]*)\/\/(?P<host>[a-zA-Z0-9-.]*)\/(?P<secure_token>\w+)\/(?P<secure_time_token>\w+)\/(?P<client>\w+)\/(?P<partner_id>[a-zA-Z0-9-]*)\/(?P<servicepriabr>[a-zA-Z0-9]*)_(?P<language>\w+).smil\/(?P<DRM_type>\w+).mpd",  # Equidia
        "mpd-1": "^(?P<https>[a-z:]*)\/\/(?P<host>[a-zA-Z0-9-.]*)\/(?P<VERSION>[a-zA-Z0-9-]*)\/(?P<key1>[a-zA-Z0-9-]*)\/(?P<key2>[a-zA-Z0-9-]*)\/(?P<video_id>[a-zA-Z0-9-]*).ism\/.mpd",  # Universcine
        "m4s-0": "^(?P<https>[a-z:]*)\/\/(?P<host>[a-zA-Z0-9-.]*)\/(?P<secure_token>\w+)\/(?P<secure_time_token>\w+)\/(?P<client>\w+)\/(?P<service_application>[a-zA-Z0-9-]*)\/(?P<servicepriabr>[a-zA-Z0-9]*)_(?P<language>\w+).smil\/(?P<slot>[a-zA-Z0-9]*)_(?P<creation_date>[a-zA-Z0-9]*)_(?P<segment_type>[a-zA-Z0-9]*)_(?P<bitrate>[a-zA-Z0-9]*)_(?P<DRM_type>[a-zA-Z0-9]*)_(?P<segment_id>[a-zA-Z0-9]*)_(?P<segment_duration>[a-zA-Z0-9]*).m4s",  # Equidia NEW
        "m4s-1": "^(?P<https>[a-z:]*)\/\/(?P<host>[a-zA-Z0-9-.]*)\/(?P<secure_token>\w+)\/(?P<secure_time_token>\w+)\/(?P<client>\w+)\/(?P<service_application>[a-zA-Z0-9-]*)\/(?P<servicepriabr>[a-zA-Z0-9]*)_(?P<language_bis>\w+).smil\/(?P<service>[a-zA-Z0-9]*)_(?P<language>[a-zA-Z0-9]*)_(?P<creation_date>[a-zA-Z0-9]*)_(?P<segment_type>[a-zA-Z0-9]*)_(?P<bitrate>[a-zA-Z0-9]*)_(?P<DRM_type>[a-zA-Z0-9]*)_(?P<segment_id>[a-zA-Z0-9]*)_(?P<segment_duration>[a-zA-Z0-9]*).m4s",  # Equidia
        "m4s-2": "^(?P<https>[a-z:]*)\/\/(?P<host>[a-zA-Z0-9-.]*)\/(?P<secure_token>\w+)\/(?P<secure_time_token>\w+)\/(?P<client>\w+)\/(?P<service_application>[a-zA-Z0-9-]*)\/(?P<servicepriabr>[a-zA-Z0-9]*)_(?P<language_bis>\w+).smil\/(?P<service>[a-zA-Z0-9]*)_(?P<language>[a-zA-Z0-9]*)_(?P<creation_date>[a-zA-Z0-9]*)_(?P<segment_type>[a-zA-Z0-9]*)_(?P<bitrate>[a-zA-Z0-9]*)_(?P<DRM_type>[a-zA-Z0-9]*)_(?P<segment_id>[a-zA-Z0-9-]*).m4s",  # Equidia
        "m4s-3": "^(?P<https>[a-z:]*)\/\/(?P<host>[a-zA-Z0-9-.]*)\/(?P<secure_token>\w+)\/(?P<secure_time_token>\w+)\/(?P<client>\w+)\/(?P<service_application>[a-zA-Z0-9-]*)\/(?P<servicepriabr>[a-zA-Z0-9]*)_(?P<language_bis>\w+).smil\/(?P<service>[a-zA-Z0-9]*)_(?P<language>[a-zA-Z0-9]*)_(?P<creation_date>[a-zA-Z0-9]*)_(?P<segment_type>[a-zA-Z0-9]*)_(?P<bitrate>[a-zA-Z0-9]*)_(?P<segment_id>[a-zA-Z0-9-]*).m4s",  # Equidia
        "m4s-4": "^(?P<https>[a-z:]*)\/\/(?P<host>[a-zA-Z0-9-.]*)\/(?P<VERSION>[a-zA-Z0-9-]*)\/(?P<key1>[a-zA-Z0-9-]*)\/(?P<key2>[a-zA-Z0-9-]*)\/(?P<video_id>[a-zA-Z0-9-]*).ism\/dash\/(?P<video_id_bis>[a-zA-Z0-9-]*)-audio_(?P<language>\w+)=(?P<quality>[a-zA-Z0-9-]*)-(?P<id>[a-zA-Z0-9-]*).m4s",  # Universcine
        "m4s-5": "^(?P<https>[a-z:]*)\/\/(?P<host>[a-zA-Z0-9-.]*)\/(?P<VERSION>[a-zA-Z0-9-]*)\/(?P<key1>[a-zA-Z0-9-]*)\/(?P<key2>[a-zA-Z0-9-]*)\/(?P<video_id>[a-zA-Z0-9-]*).ism\/dash\/(?P<video_id_bis>[a-zA-Z0-9-]*)-video=(?P<quality>[a-zA-Z0-9-]*)-(?P<id>[a-zA-Z0-9-]*).m4s",  # Universcine
        "m4s-6": "^(?P<https>[a-z:]*)\/\/(?P<host>[a-zA-Z0-9-.]*)\/(?P<VERSION>[a-zA-Z0-9-]*)\/(?P<key1>[a-zA-Z0-9-]*)\/(?P<key2>[a-zA-Z0-9-]*)\/(?P<video_id>[a-zA-Z0-9-]*).ism\/dash\/(?P<video_id_bis>[a-zA-Z0-9-]*)-textstream_(?P<language>\w+)=(?P<index>[a-zA-Z0-9-]*)-(?P<id>[a-zA-Z0-9-]*).m4s",  # Universcine
        "ts-0": "^(?P<https>[a-z:]*)\/\/(?P<host>[a-zA-Z0-9-.]*)\/(?P<secure_token>\w+)\/(?P<secure_time_token>\w+)\/(?P<client>\w+)\/(?P<service_application>[a-zA-Z0-9-]*)\/(?P<servicepriabr>[a-zA-Z0-9]*)_(?P<language_bis>\w+).smil\/(?P<slot>[a-zA-Z0-9]*)_audio_(?P<language>[a-zA-Z0-9]*)_(?P<creation_date>[a-zA-Z0-9]*)_(?P<id>[a-zA-Z0-9]*)_(?P<ts_length>[a-zA-Z0-9]*)_(?P<segment_duration>\w+)",  # Equidia NEW
        "ts-0BIS": "^(?P<https>[a-z:]*)\/\/(?P<host>[a-zA-Z0-9-.]*)\/(?P<secure_token>\w+)\/(?P<secure_time_token>\w+)\/(?P<client>\w+)\/(?P<service_application>[a-zA-Z0-9-]*)\/(?P<servicepriabr>[a-zA-Z0-9]*)_(?P<language>\w+).smil\/(?P<slot>[a-zA-Z0-9]*)_(?P<quality>[a-zA-Z0-9]*)_(?P<creation_date>[a-zA-Z0-9]*)_(?P<id>[a-zA-Z0-9]*)_(?P<ts_length>[a-zA-Z0-9]*)_(?P<segment_duration>\w+)",  # Equidia NEW
        "ts-1": "^(?P<https>[a-z:]*)\/\/(?P<host>[a-zA-Z0-9-.]*)\/(?P<secure_token>\w+)\/(?P<secure_time_token>\w+)\/(?P<client>\w+)\/(?P<service_application>[a-zA-Z0-9-]*)\/(?P<servicepriabr>[a-zA-Z0-9]*)_(?P<language_bis>\w+).smil\/(?P<service>[a-zA-Z0-9]*)_(?P<language>[a-zA-Z0-9]*)_(?P<bitrate>[a-zA-Z0-9]*)_(?P<creation_date>[a-zA-Z0-9]*)_(?P<id>[a-zA-Z0-9]*)_(?P<ts_length>[a-zA-Z0-9]*)_(?P<segment_duration>\w+)",  # Equidia
        "ts-2": "^(?P<https>[a-z:]*)\/\/(?P<host>[a-zA-Z0-9-.]*)\/(?P<secure_token>\w+)\/(?P<secure_time_token>\w+)\/(?P<client>\w+)\/(?P<service_application>[a-zA-Z0-9-]*)\/(?P<servicepriabr>[a-zA-Z0-9]*)_(?P<language_bis>\w+).smil\/(?P<service>[a-zA-Z0-9]*)_(?P<language>[a-zA-Z0-9]*)_(?P<creation_date>[a-zA-Z0-9]*)_(?P<video>[a-zA-Z0-9]*)_(?P<bitrate>[a-zA-Z0-9]*)_(?P<drm_ciphers>[a-zA-Z0-9]*)_(?P<id>\w+).ts",  # Equidia
        "ts-3": "^(?P<https>[a-z:]*)\/\/(?P<host>[a-zA-Z0-9-.]*)\/(?P<secure_token>\w+)\/(?P<secure_time_token>\w+)\/(?P<service>[a-zA-Z0-9]*)_(?P<language>[a-zA-Z0-9]*)_(?P<quality>[a-zA-Z0-9]*)_(?P<creation_date>[a-zA-Z0-9]*)_(?P<id>[a-zA-Z0-9]*)_(?P<ts_length>[a-zA-Z0-9]*)_(?P<segment_duration>\w+).ts",  # Equidia
        "ts-4": "^(?P<https>[a-z:]*)\/\/(?P<host>[a-zA-Z0-9-.]*)\/(?P<secure_token>\w+)\/(?P<secure_time_token>\w+)\/(?P<client>\w+)\/(?P<service_application>[a-zA-Z0-9-]*)\/(?P<servicepriabr>[a-zA-Z0-9]*)_(?P<language_bis>\w+).smil\/(?P<service>[a-zA-Z0-9]*)_(?P<language>[a-zA-Z0-9]*)_(?P<bitrate>[a-zA-Z0-9]*)_(?P<creation_date>[a-zA-Z0-9]*)_(?P<id>\w+).ts",  # Equidia
        "ts-5": "^(?P<https>[a-z:]*)\/\/(?P<host>[a-zA-Z0-9-.]*)\/(?P<secure_token>\w+)\/(?P<secure_time_token>\w+)\/(?P<service>[a-zA-Z0-9]*)_(?P<language>[a-zA-Z0-9]*)_(?P<creation_date>[a-zA-Z0-9]*)_video_(?P<bitrate>[a-zA-Z0-9]*)_(?P<drm_ciphers>[a-zA-Z0-9]*)_(?P<id>\w+).ts",  # Equidia
        "ts-6": "^(?P<https>[a-z:]*)\/\/(?P<host>[a-zA-Z0-9-.]*)\/dl\/(?P<secure_token>\w+)\/(?P<secure_time_token>\w+)\/(?P<ovp_service>[a-zA-Z0-9-_]*)\/videos\/hls\/(?P<h1>\w+)\/(?P<h2>\w+)\/(?P<S_playlist_id>\w+)\/(?P<V_playlist_id>\w+)\/(?P<s_scene>\w+)\/(?P<s_scene_id>[a-zA-Z0-9-]*).ts",  # Equidia
        "ts-7": "^(?P<https>[a-z:]*)\/\/(?P<host>[a-zA-Z0-9-.]*)\/dl\/(?P<secure_token>\w+)\/(?P<secure_time_token>\w+)\/(?P<ovp_service>[a-zA-Z0-9-_]*)\/hls\/(?P<h1>\w+)\/(?P<h2>\w+)\/(?P<playlist_id>\w+)\/(?P<s_scene>\w+)\/(?P<s_scene_id>[a-zA-Z0-9-]*).ts",  # Equidia
        "ts-8": "^(?P<https>[a-z:]*)\/\/(?P<host>[a-zA-Z0-9-.]*)\/dl\/(?P<secure_token>\w+)\/(?P<secure_time_token>\w+)\/(?P<ovp_service>[a-zA-Z0-9-_]*)\/hls\/(?P<h1>\w+)\/(?P<h2>\w+)\/(?P<playlist_id>\w+)\/(?P<s_scene_id>[a-zA-Z0-9-]*).ts",  # Equidia
        "ts-9": "^(?P<https>[a-z:]*)\/\/(?P<host>[a-zA-Z0-9-.]*)\/(?P<VERSION>[a-zA-Z0-9-]*)\/(?P<key1>[a-zA-Z0-9-]*)\/(?P<key2>[a-zA-Z0-9-]*)\/(?P<video_id>[a-zA-Z0-9-]*).ism\/(?P<video_id_bis>[a-zA-Z0-9-]*)-audio_(?P<language>\w+)=(?P<quality>[a-zA-Z0-9-]*)-(?P<id>[a-zA-Z0-9-]*).ts",  # Universcine
        "ts-10": "^(?P<https>[a-z:]*)\/\/(?P<host>[a-zA-Z0-9-.]*)\/(?P<VERSION>[a-zA-Z0-9-]*)\/(?P<key1>[a-zA-Z0-9-]*)\/(?P<key2>[a-zA-Z0-9-]*)\/(?P<video_id>[a-zA-Z0-9-]*).ism\/(?P<video_id_bis>[a-zA-Z0-9-]*)-audio_(?P<language>\w+)=(?P<qualityaudio>[a-zA-Z0-9-]*)-video=(?P<quality>[a-zA-Z0-9-]*)-(?P<id>[a-zA-Z0-9-]*).ts",  # Universcine
        "mp4-0": "^(?P<https>[a-z:]*)\/\/(?P<host>[a-zA-Z0-9-.]*)\/(?P<secure_token>\w+)\/(?P<secure_time_token>\w+)\/(?P<client>\w+)\/(?P<service_application>[a-zA-Z0-9-]*)\/(?P<servicepriabr>[a-zA-Z0-9]*)_(?P<language_bis>\w+).smil\/(?P<service>[a-zA-Z0-9]*)_(?P<creation_date>[a-zA-Z0-9]*)_(?P<segment_type>[a-zA-Z0-9]*)_(?P<bitrate>[a-zA-Z0-9]*)_(?P<DRM_type>[a-zA-Z0-9]*).mp4",  # Equidia NEW
        "mp4": "^(?P<https>[a-z:]*)\/\/(?P<host>[a-zA-Z0-9-.]*)\/(?P<secure_token>\w+)\/(?P<secure_time_token>\w+)\/(?P<client>\w+)\/(?P<service_application>[a-zA-Z0-9-]*)\/(?P<servicepriabr>[a-zA-Z0-9]*)_(?P<language_bis>\w+).smil\/(?P<service>[a-zA-Z0-9]*)_(?P<language>[a-zA-Z0-9]*)_(?P<creation_date>[a-zA-Z0-9]*)_(?P<segment_type>[a-zA-Z0-9]*)_(?P<bitrate>[a-zA-Z0-9]*)_(?P<DRM_type>[a-zA-Z0-9]*).mp4",  # Equidia
        "mp4-2": "^(?P<https>[a-z:]*)\/\/(?P<host>[a-zA-Z0-9-.]*)\/(?P<secure_token>\w+)\/(?P<secure_time_token>\w+)\/(?P<client>\w+)\/(?P<service_application>[a-zA-Z0-9-]*)\/(?P<servicepriabr>[a-zA-Z0-9]*)_(?P<language_bis>\w+).smil\/(?P<service>[a-zA-Z0-9]*)_(?P<language>[a-zA-Z0-9]*)_(?P<creation_date>[a-zA-Z0-9]*)_(?P<segment_type>[a-zA-Z0-9]*)_(?P<bitrate>[a-zA-Z0-9]*).mp4",  # Equidia
    }

    _COMPILE_REGEXES = []

    def compile_patterns():
        for key, pattern in RegexChecker._PATTERNS.items():
            RegexChecker._COMPILE_REGEXES.append(re.compile(pattern))

    def _get_timestamp_from_stream_date(date: str):
        try:
            # return datetime.datetime.strptime(date + '+0000', '%Y%m%d%H%M%S%z').timestamp() * 1e3

            # Following method to extract timestamp is longer and uglier than the one above but SO MUCH FASTER

            # Extract components from the time string
            year = int(date[0:4])
            month = int(date[4:6])
            day = int(date[6:8])
            hour = int(date[8:10])
            minute = int(date[10:12])
            try:
                second = int(date[12:14])
            except Exception:
                second = 0
            # Calculate timestamp directly
            timestamp = (year - 1970) * 31536000  # seconds in a year
            timestamp += ((year - 1969) // 4) * 86400  # add leap days
            timestamp += (
                sum(
                    [
                        31,
                        28 + (year % 4 == 0 and (year % 100 != 0 or year % 400 == 0)),
                        31,
                        30,
                        31,
                        30,
                        31,
                        31,
                        30,
                        31,
                        30,
                        31,
                    ][: month - 1]
                )
                * 86400
            )  # add months
            timestamp += (day - 1) * 86400  # add days
            timestamp += hour * 3600  # add hours
            timestamp += minute * 60  # add minutes
            timestamp += second  # add seconds
            timestamp *= 1000  # convert to milliseconds

            return timestamp

        except Exception:
            return 0

    def check_uri(uri: str):
        if uri:
            for key, pattern in enumerate(RegexChecker._COMPILE_REGEXES):
                res = None
                try:
                    key = RegexChecker._EXTENSIONS[key]
                    if ("." + key.split("-")[0]) in uri:
                        res = pattern.match(uri)
                    if res is None:
                        continue
                    else:
                        res = res.groupdict()
                        if "secure_token" in res and "secure_time_token" in res:
                            uri = uri.replace(res["secure_token"], "secure_token")
                            uri = uri.replace(
                                res["secure_time_token"], "secure_time_token"
                            )
                        stream_protocol = ""
                        drm_type = ""
                        client = ""
                        service_id = ""
                        playlist_id = ""
                        stream_name = ""
                        partner_id = ""
                        language = ""
                        server_name = ""
                        extension = ""
                        creation_date = ""
                        segment_type = ""
                        bitrate = ""
                        segment_id = 0
                        segment_duration = 0
                        total_time_after_creation = 0

                        if key in [
                            "m3u8-4",
                            "m3u8-5",
                            "mpd-1",
                            "m4s-4",
                            "m4s-5",
                            "m4s-6",
                            "ts-9",
                            "ts-10",
                        ]:
                            client = "universcine"
                            service_id = "universcine-streaming-vod"

                        if "playlist_id" in res:
                            playlist_id = res["playlist_id"]
                        elif "S_playlist_id" in res:
                            playlist_id = (
                                res["S_playlist_id"] + "-" + res["V_playlist_id"]
                            )

                        if "m3u8" in key:
                            stream_protocol = "HLS"
                            extension = "m3u8"
                        elif "mpd" in key:
                            stream_protocol = "DASH"
                            extension = "mpd"
                        elif "m4s" in key:
                            extension = "m4s"
                        elif "ts" in key:
                            extension = "ts"
                        elif "mp4" in key:
                            extension = "mp4"
                        if "DRM_type" in res:
                            drm_type = res["DRM_type"]
                        if "client" in res:
                            client = res["client"]
                        elif "ovp_service" in res:
                            client = res["ovp_service"].split("_")[0]
                        if "partner_id" in res:
                            partner_id = res["partner_id"]
                        if "servicepriabr" in res and "language" in res:
                            stream_name = res["servicepriabr"] + "_" + res["language"]
                        if "language" in res:
                            language = res["language"]
                        if "host" in res:
                            server_name = res["host"]
                        if "creation_date" in res and res["creation_date"].isnumeric():
                            creation_date = int(
                                RegexChecker._get_timestamp_from_stream_date(
                                    res["creation_date"]
                                )
                            )
                        if "segment_type" in res:
                            segment_type = res["segment_type"]
                        if "bitrate" in res:
                            bitrate = res["bitrate"]

                        if "quality" in res:
                            bitrate = res["quality"]

                        if "segment_id" in res:
                            segment_id = int(res["segment_id"])
                        #    try:
                        #        segment_id = int(res['segment_id'])
                        #    except:
                        #        print("Error in parsing of segment_id !!! ", uri, res, flush=True)
                        if (
                            "segment_duration" in res
                            and res["segment_duration"].isnumeric()
                        ):
                            segment_duration = float(
                                res["segment_duration"][0:7]
                                + "."
                                + res["segment_duration"][7:]
                            )
                            total_time_after_creation = round(
                                (segment_duration * segment_id) * 1000
                            )
                            creation_date = creation_date + total_time_after_creation

                        if len(server_name.split("-")) > 1 and (
                            "service" in res or "servicepriabr" in res
                        ):
                            service_id = (
                                client + "-" + server_name.split("-")[1] + "-live"
                            )
                        elif "ovp_service" in res:
                            service_id = (
                                "ovp-"
                                + res["ovp_service"].split("_")[0]
                                + "-"
                                + res["ovp_service"].split("_")[1]
                            )

                        return (
                            uri,
                            client,
                            service_id,
                            pattern,
                            stream_protocol,
                            drm_type,
                            playlist_id,
                            stream_name,
                            partner_id,
                            language,
                            server_name,
                            extension,
                            segment_type,
                            bitrate,
                            segment_duration,
                            creation_date,
                        )
                except Exception as e:
                    print("Error in parsing of uri : ", e, uri, flush=True)
            return (
                uri,
                None,
                None,
                None,
                None,
                None,
                None,
                None,
                None,
                None,
                None,
                None,
                None,
                None,
                None,
                None,
            )
        else:
            return (
                uri,
                None,
                None,
                None,
                None,
                None,
                None,
                None,
                None,
                None,
                None,
                None,
                None,
                None,
                None,
                None,
            )
